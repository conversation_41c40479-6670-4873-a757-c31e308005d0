2025-08-13 11:09:40,961 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:94] - ⚠️  安全提醒：开发模式下所有接口无需认证即可访问
2025-08-13 11:09:41,225 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:105] - ⚠️  配置开发模式安全策略：所有接口均可无认证访问
2025-08-13 11:09:41,227 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:116] - ⚠️  开发模式安全配置已生效，请勿在生产环境使用
2025-08-13 11:17:28,446 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:94] - ⚠️  安全提醒：开发模式下所有接口无需认证即可访问
2025-08-13 11:17:28,695 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:105] - ⚠️  配置开发模式安全策略：所有接口均可无认证访问
2025-08-13 11:17:28,698 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:116] - ⚠️  开发模式安全配置已生效，请勿在生产环境使用
2025-08-13 12:41:03,329 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:94] - ⚠️  安全提醒：开发模式下所有接口无需认证即可访问
2025-08-13 12:41:03,581 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:105] - ⚠️  配置开发模式安全策略：所有接口均可无认证访问
2025-08-13 12:41:03,584 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:116] - ⚠️  开发模式安全配置已生效，请勿在生产环境使用
2025-08-13 14:45:01,860 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-13 14:45:02,137 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:58] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-13 14:45:02,140 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:69] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-13 15:56:59,340 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-08-13 16:01:01,973 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-08-13 16:01:49,670 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-08-13 16:06:28,200 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-08-13 16:09:02,596 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-08-13 16:15:39,152 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.boot.SpringApplication [L:839] - Unable to close ApplicationContext
org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [cathayfuture.opm.adapter.config.WxMaConfiguration] for bean with name 'wxMaConfiguration' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/config/WxMaConfiguration.class]; nested exception is java.lang.ClassNotFoundException: cathayfuture.opm.adapter.config.WxMaConfiguration
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveBeanClass(AbstractBeanFactory.java:1392)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:680)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:647)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1518)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:507)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:477)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:598)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeansOfType(DefaultListableBeanFactory.java:590)
	at org.springframework.context.support.AbstractApplicationContext.getBeansOfType(AbstractApplicationContext.java:1226)
	at org.springframework.boot.SpringApplication.getExitCodeFromMappedException(SpringApplication.java:905)
	at org.springframework.boot.SpringApplication.getExitCodeFromException(SpringApplication.java:891)
	at org.springframework.boot.SpringApplication.handleExitCode(SpringApplication.java:877)
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:826)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:327)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at cathayfuture.opm.App.main(App.java:23)
Caused by: java.lang.ClassNotFoundException: cathayfuture.opm.adapter.config.WxMaConfiguration
	at java.net.URLClassLoader.findClass(URLClassLoader.java:387)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:419)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:352)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:352)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:348)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:275)
	at org.springframework.beans.factory.support.AbstractBeanDefinition.resolveBeanClass(AbstractBeanDefinition.java:437)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doResolveBeanClass(AbstractBeanFactory.java:1457)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveBeanClass(AbstractBeanFactory.java:1384)
	... 16 common frames omitted
2025-08-13 16:16:16,846 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-13 16:16:16,851 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bmsAuthController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginRateLimiter' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/security/LoginRateLimiter.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-13 16:19:04,078 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-13 16:19:04,082 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bmsAuthController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginRateLimiter' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/security/LoginRateLimiter.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-13 16:19:50,155 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-13 16:19:50,159 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bmsAuthController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginRateLimiter' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/security/LoginRateLimiter.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-13 16:22:26,772 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-13 16:22:26,776 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bmsAuthController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loginRateLimiter' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/security/LoginRateLimiter.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-13 16:38:12,767 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-08-13 16:48:36,961 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-13 16:48:37,217 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:58] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-13 16:48:37,219 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:69] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-13 16:49:24,944 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-13 16:49:25,212 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:58] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-13 16:49:25,215 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:69] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-13 16:50:37,245 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'bmsAuthController' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/bms/auth/controller/BmsAuthController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.security.authentication.AuthenticationManager' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-13 17:31:58,136 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-13 17:31:58,395 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:58] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-13 17:31:58,397 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:69] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-13 17:35:25,186 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-13 17:35:25,447 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:58] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-13 17:35:25,449 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:69] - ⚠️  开发环境安全配置已生效，所有接口无需认证
