# 测试控制器Profile配置验证文档

## 配置说明

所有测试控制器现在都使用 `@Profile({"!prod"})` 配置，确保在生产环境中完全禁用。

## 修改的文件

### 1. RateLimitTestController.java
- 路径：`/bms/test/rate-limit`
- 功能：测试登录速率限制
- Profile配置：`@Profile({"!prod"})`
- 环境支持：dev, test, uat, local 等（除prod外所有环境）

### 2. TokenIndependenceTestController.java
- 路径：`/bms/test/token`
- 功能：验证BMS token与小程序token的独立性
- Profile配置：`@Profile({"!prod"})`
- 环境支持：dev, test, uat, local 等（除prod外所有环境）

### 3. UserContextTestController.java
- 路径：`/bms/test/userContext`
- 功能：测试用户上下文信息获取
- Profile配置：`@Profile({"!prod"})`
- 环境支持：dev, test, uat, local 等（除prod外所有环境）

## 验证方法

### 开发/测试环境验证
```bash
# 设置非生产环境（这些接口应该可访问）
export SPRING_PROFILES_ACTIVE=dev
# 或
export SPRING_PROFILES_ACTIVE=test
# 或
export SPRING_PROFILES_ACTIVE=uat

# 启动应用后，以下接口应该可以访问
curl -X GET http://localhost:8080/bms/test/rate-limit/status?username=test
curl -X GET http://localhost:8080/bms/test/token/independence
curl -X GET http://localhost:8080/bms/test/userContext/extServiceContext
```

### 生产环境验证
```bash
# 设置生产环境（这些接口应该404）
export SPRING_PROFILES_ACTIVE=prod

# 启动应用后，以下接口应该返回404 Not Found
curl -X GET http://localhost:8080/bms/test/rate-limit/status?username=test
curl -X GET http://localhost:8080/bms/test/token/independence
curl -X GET http://localhost:8080/bms/test/userContext/extServiceContext
```

## 安全特性

1. **自动环境检测**：Spring会根据`spring.profiles.active`自动决定是否加载这些控制器
2. **完全禁用**：在生产环境中，这些类不会被Spring容器实例化，接口完全不存在
3. **零配置风险**：即使忘记配置，生产环境也是安全的（需要显式启用非prod profile）
4. **Swagger文档隔离**：生产环境的API文档中不会出现这些测试接口

## 最佳实践

- ✅ 使用 `@Profile({"!prod"})` 排除生产环境
- ✅ 在类注释中明确说明环境限制
- ✅ 添加安全提醒注释
- ✅ 使用Swagger注解标记测试接口
- ❌ 避免使用硬编码的环境列表（如 `@Profile({"dev", "test", "uat"})`）

## 注意事项

1. 确保生产环境的 `spring.profiles.active=prod`
2. 其他环境可以使用任何非"prod"的profile名称
3. 可以同时激活多个profile，只要不包含"prod"即可
4. 测试时建议先验证生产环境配置，确认接口确实被禁用