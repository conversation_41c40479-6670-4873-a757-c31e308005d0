2025-08-14 08:57:03,913 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 08:57:04,175 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:58] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 08:57:04,178 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:69] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 08:58:40,580 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 08:58:40,717 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext [L:557] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'loggingAuthenticationProvider' defined in file [/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes/cathayfuture/opm/adapter/security/LoggingAuthenticationProvider.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'org.springframework.security.core.userdetails.UserDetailsService' available: expected single matching bean but found 2: databaseUserDetailsService,fixedUserDetailsService
2025-08-14 08:59:50,485 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 08:59:50,749 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:58] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 08:59:50,751 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:69] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 09:05:49,707 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 09:05:49,979 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:58] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 09:05:49,982 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:69] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 09:22:28,781 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:57] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 09:22:29,044 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:63] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 09:22:29,046 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:74] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 09:22:52,046 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:57] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 09:22:52,309 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:63] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 09:22:52,311 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:74] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 09:42:19,323 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 09:42:19,595 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:66] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 09:42:19,598 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:77] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 10:02:52,579 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 10:02:52,838 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 10:02:52,841 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 10:17:59,502 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 10:17:59,759 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 10:17:59,761 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 10:55:14,902 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 10:55:15,158 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 10:55:15,160 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 12:33:19,229 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 12:33:19,515 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 12:33:19,517 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 12:46:14,079 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 12:46:14,341 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 12:46:14,343 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 12:47:49,810 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 12:47:50,073 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 12:47:50,075 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 12:48:29,891 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 12:48:30,159 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 12:48:30,161 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 12:56:55,584 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 12:56:55,859 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 12:56:55,861 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 12:58:19,221 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 12:58:19,469 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 12:58:19,471 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 13:02:00,274 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 13:02:00,536 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 13:02:00,538 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 13:02:28,488 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 13:02:28,775 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 13:02:28,777 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 13:04:32,799 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 13:04:33,070 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 13:04:33,072 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 13:14:44,486 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 13:14:44,747 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 13:14:44,749 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 13:14:51,691 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitService [L:146] - 通用API限流触发 - IP: 0:0:***, API: /bms/studentClass/queryAllClassNames, 当前次数: 4/3
2025-08-14 13:14:51,692 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:66] - RateLimitInterceptor - 限流触发，拒绝请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:14:52,166 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitService [L:146] - 通用API限流触发 - IP: 0:0:***, API: /bms/studentClass/queryAllClassNames, 当前次数: 4/3
2025-08-14 13:14:52,167 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:66] - RateLimitInterceptor - 限流触发，拒绝请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:14:53,157 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitService [L:146] - 通用API限流触发 - IP: 0:0:***, API: /bms/studentClass/queryAllClassNames, 当前次数: 4/3
2025-08-14 13:14:53,158 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:66] - RateLimitInterceptor - 限流触发，拒绝请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:16:09,395 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 13:16:09,651 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 13:16:09,654 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 13:18:31,463 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 13:18:31,735 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 13:18:31,737 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 13:18:37,249 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitService [L:146] - 通用API限流触发 - IP: 0:0:***, API: /bms/auth/login, 当前次数: 4/3
2025-08-14 13:18:37,250 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:66] - RateLimitInterceptor - 限流触发，拒绝请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:37,814 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitService [L:146] - 通用API限流触发 - IP: 0:0:***, API: /bms/auth/login, 当前次数: 4/3
2025-08-14 13:18:37,814 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:66] - RateLimitInterceptor - 限流触发，拒绝请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:37,996 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitService [L:146] - 通用API限流触发 - IP: 0:0:***, API: /bms/auth/login, 当前次数: 4/3
2025-08-14 13:18:37,997 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:66] - RateLimitInterceptor - 限流触发，拒绝请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,180 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitService [L:146] - 通用API限流触发 - IP: 0:0:***, API: /bms/auth/login, 当前次数: 4/3
2025-08-14 13:18:38,180 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:66] - RateLimitInterceptor - 限流触发，拒绝请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,365 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitService [L:146] - 通用API限流触发 - IP: 0:0:***, API: /bms/auth/login, 当前次数: 4/3
2025-08-14 13:18:38,366 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:66] - RateLimitInterceptor - 限流触发，拒绝请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,554 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitService [L:146] - 通用API限流触发 - IP: 0:0:***, API: /bms/auth/login, 当前次数: 4/3
2025-08-14 13:18:38,555 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:66] - RateLimitInterceptor - 限流触发，拒绝请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,736 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitService [L:146] - 通用API限流触发 - IP: 0:0:***, API: /bms/auth/login, 当前次数: 4/3
2025-08-14 13:18:38,738 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:66] - RateLimitInterceptor - 限流触发，拒绝请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:34:44,618 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 13:34:44,880 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 13:34:44,883 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 13:36:59,304 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 13:36:59,559 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 13:36:59,562 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
2025-08-14 14:37:55,799 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:54] - ⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用
2025-08-14 14:37:56,091 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:60] - ⚠️  配置开发环境安全策略：所有接口均可无认证访问
2025-08-14 14:37:56,094 WARN  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:71] - ⚠️  开发环境安全配置已生效，所有接口无需认证
