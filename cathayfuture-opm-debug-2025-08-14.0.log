2025-08-14 08:57:01,519 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 08:57:02,832 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 08:58:38,193 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 08:58:39,485 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 08:59:48,053 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 08:59:49,396 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 09:05:47,259 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 09:05:48,602 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 09:22:26,344 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 09:22:27,652 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 09:22:33,062 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:46] - === 开始数据库用户认证流程 ===
2025-08-14 09:22:33,062 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:47] - 请求认证的用户名: ad***n
2025-08-14 09:22:33,217 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:56] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 09:22:33,217 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:57] - 用户编码: ADMIN001
2025-08-14 09:22:33,218 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:58] - 真实姓名: 系**
2025-08-14 09:22:33,218 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:59] - OA姓名: 系**
2025-08-14 09:22:33,218 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:60] - 用户状态: 1
2025-08-14 09:22:33,218 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:64] - 密码哈希长度: 60
2025-08-14 09:22:33,218 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:65] - 密码哈希格式: $2a$10$FxZ
2025-08-14 09:22:33,225 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:147] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 09:22:33,227 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:124] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 09:22:33,299 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 09:22:33,339 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755134553333
2025-08-14 09:22:33,343 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 09:22:33,344 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 09:22:33,351 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 09:22:33,356 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755134553333
2025-08-14 09:22:33,358 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 09:22:49,692 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 09:22:50,979 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 09:24:50,701 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:218] - 删除用户token映射: username=admin
2025-08-14 09:24:58,327 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.TokenManagementService [L:168] - Token在Redis中不存在: eyJ0eXAiOiJKV1QiLCJh
2025-08-14 09:24:58,328 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.JwtAuthenticationFilter [L:66] - Token在Redis中不存在或已失效
2025-08-14 09:42:16,820 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 09:42:18,178 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 10:02:50,045 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 10:02:51,388 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 10:17:57,061 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 10:17:58,367 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 10:18:31,946 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 10:18:31,947 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 10:18:31,961 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 10:18:31,961 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 10:18:31,961 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 10:18:31,961 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 10:18:31,961 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 10:18:31,962 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 10:18:31,962 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 10:18:31,969 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 10:18:31,972 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 10:18:32,045 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 10:18:32,083 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755137912078
2025-08-14 10:18:32,085 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 10:18:32,087 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 10:18:32,089 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755137912078
2025-08-14 10:18:32,091 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 10:19:41,518 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 10:19:42,903 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 10:19:59,293 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 10:19:59,294 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 10:19:59,345 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 10:19:59,345 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 10:19:59,345 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 10:19:59,346 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 10:19:59,346 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 10:19:59,346 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 10:19:59,346 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 10:19:59,351 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 10:19:59,354 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 10:19:59,424 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 10:19:59,463 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755137999457
2025-08-14 10:19:59,465 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 10:19:59,467 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 10:19:59,470 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 10:19:59,472 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755137999457
2025-08-14 10:19:59,474 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 10:20:18,393 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:176] - Token验证成功: username=admin
2025-08-14 10:20:18,402 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.JwtAuthenticationFilter [L:57] - JWT认证成功，用户: ad***n, 权限数量: 1
2025-08-14 10:20:44,047 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:218] - 删除用户token映射: username=admin
2025-08-14 10:20:48,841 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:168] - Token在Redis中不存在: eyJ0eXAiOiJKV1QiLCJh
2025-08-14 10:20:48,842 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.JwtAuthenticationFilter [L:66] - Token在Redis中不存在或已失效
2025-08-14 10:55:12,396 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 10:55:13,751 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 12:33:16,446 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 12:33:17,949 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 12:46:11,572 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 12:46:12,966 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 12:47:47,327 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 12:47:48,675 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 12:48:27,332 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 12:48:28,739 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 12:56:53,200 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 12:56:54,495 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 12:57:23,833 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:23,834 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:25,231 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:25,232 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:25,419 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:25,420 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:26,036 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:26,036 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:27,084 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:27,084 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:28,534 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:28,535 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:29,121 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:29,121 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:29,761 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:29,762 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:30,380 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:30,381 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:31,013 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:31,014 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:31,602 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:31,602 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:32,189 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:32,189 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:32,754 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:32,754 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:33,318 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:33,319 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:33,960 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:33,961 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:34,642 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:34,643 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:35,244 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:35,245 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:36,047 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:36,048 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:36,638 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:36,638 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:38,357 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:38,357 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:38,657 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:38,658 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:57:39,187 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:57:39,188 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:16,807 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 12:58:18,134 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 12:58:23,833 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:23,833 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:24,292 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:24,293 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:24,909 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:24,910 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:25,476 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:25,477 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:26,101 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:26,102 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:29,179 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:29,179 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:29,382 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:29,383 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:30,712 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:30,713 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:31,316 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:31,317 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:31,496 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:31,497 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:37,360 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:37,361 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:37,563 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:37,564 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:38,005 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:38,005 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:40,501 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:40,501 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:40,672 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:40,672 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:41,211 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:41,212 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:41,386 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:41,386 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:41,540 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:41,541 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:41,744 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:41,744 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:41,902 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:41,902 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:42,080 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:42,080 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:42,247 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:42,248 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:42,442 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:42,442 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:42,621 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:42,621 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:42,775 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:42,776 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:42,953 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:42,954 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:43,138 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:43,138 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:43,317 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:43,317 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:43,495 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:43,496 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:43,667 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:43,667 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:43,846 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:43,847 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:44,063 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:44,063 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:44,243 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:44,243 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:44,393 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:44,393 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:44,606 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:44,607 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:45,008 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:45,008 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:45,179 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:45,180 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:45,395 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:45,395 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:45,538 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:45,539 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:45,749 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:45,750 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:45,923 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:45,924 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:46,114 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:46,115 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:46,284 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:46,285 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:46,470 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:46,471 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:46,671 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:46,671 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:46,857 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:46,857 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 12:58:47,026 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 12:58:47,026 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:01:57,809 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 13:01:59,166 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 13:02:03,474 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:03,475 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:04,683 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:04,684 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:05,263 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:05,264 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:07,633 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:07,633 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:08,161 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:08,161 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:08,696 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:08,697 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:09,220 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:09,221 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:09,803 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:09,803 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:10,350 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:10,351 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:10,900 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:10,901 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:11,475 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:11,475 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:12,049 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:12,049 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:26,002 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 13:02:27,328 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 13:02:35,021 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:35,021 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:35,688 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:35,689 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:36,222 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:36,223 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:37,211 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:37,212 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:37,741 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:37,742 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:38,272 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:38,273 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:38,760 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:38,761 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:39,780 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:39,781 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:40,327 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:40,328 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:40,814 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:40,815 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:41,890 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:41,891 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:42,399 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:42,399 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:42,958 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:42,959 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:43,258 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:43,259 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:43,497 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:43,497 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:43,709 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:43,709 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:43,935 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:43,936 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:44,199 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:44,200 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:44,378 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:44,379 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:44,581 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:44,582 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:44,770 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:44,771 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:44,982 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:44,982 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:45,177 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:45,177 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:45,362 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:45,362 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:45,564 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:45,565 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:45,763 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:45,764 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:45,958 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:45,958 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:46,174 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:46,175 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:46,371 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:46,371 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:46,578 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:46,578 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:47,023 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:47,024 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:47,175 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:47,176 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:02:47,379 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:02:47,379 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:30,164 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 13:04:31,603 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 13:04:37,561 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:37,561 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:38,008 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:38,009 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:38,447 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:38,448 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:39,368 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:39,369 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:39,813 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:39,813 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:40,350 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:40,350 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:40,547 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:40,547 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:41,012 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:41,013 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:41,273 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:41,274 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:41,461 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:41,461 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:41,699 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:41,699 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:41,891 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:41,892 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:42,096 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:42,097 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:42,318 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:42,319 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:42,532 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:42,532 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:04:42,922 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:04:42,922 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:14:41,954 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 13:14:43,380 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 13:14:49,776 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:14:49,777 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/studentClass/queryAllClassNames
2025-08-14 13:14:49,798 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitService [L:154] - 通用API访问记录 - IP: 0:0:***, API: /bms/studentClass/queryAllClassNames, 当前次数: 1/3
2025-08-14 13:14:49,798 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:14:50,180 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:14:50,180 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/studentClass/queryAllClassNames
2025-08-14 13:14:50,189 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitService [L:154] - 通用API访问记录 - IP: 0:0:***, API: /bms/studentClass/queryAllClassNames, 当前次数: 2/3
2025-08-14 13:14:50,190 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:14:51,157 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:14:51,157 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/studentClass/queryAllClassNames
2025-08-14 13:14:51,161 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitService [L:154] - 通用API访问记录 - IP: 0:0:***, API: /bms/studentClass/queryAllClassNames, 当前次数: 3/3
2025-08-14 13:14:51,161 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:14:51,688 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:14:51,689 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/studentClass/queryAllClassNames
2025-08-14 13:14:52,163 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:14:52,164 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/studentClass/queryAllClassNames
2025-08-14 13:14:53,154 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:14:53,154 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/studentClass/queryAllClassNames
2025-08-14 13:16:06,897 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 13:16:08,264 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 13:17:14,816 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:14,817 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:14,833 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:14,833 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:14,834 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:14,902 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:14,902 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:14,950 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:14,950 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:14,951 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:14,951 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:14,951 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:14,951 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:14,951 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:14,957 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:14,959 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:15,032 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:15,077 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148635070
2025-08-14 13:17:15,080 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:15,084 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:15,087 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148635070
2025-08-14 13:17:15,089 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:15,904 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:15,904 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:15,911 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:15,911 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:15,912 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:15,917 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:15,918 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:15,924 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:15,924 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:15,924 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:15,924 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:15,924 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:15,924 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:15,924 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:15,928 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:15,930 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:16,017 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:16,028 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148636025
2025-08-14 13:17:16,031 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:16,032 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:16,035 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:16,038 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148636025
2025-08-14 13:17:16,040 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:17,400 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:17,401 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:17,406 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:17,406 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:17,407 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:17,411 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:17,412 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:17,421 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:17,422 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:17,422 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:17,422 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:17,422 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:17,422 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:17,422 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:17,428 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:17,430 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:17,514 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:17,538 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148637533
2025-08-14 13:17:17,540 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:17,542 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:17,544 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:17,546 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148637533
2025-08-14 13:17:17,548 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:18,564 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:18,564 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:18,569 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:18,569 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:18,570 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:18,575 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:18,575 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:18,581 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:18,581 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:18,582 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:18,582 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:18,582 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:18,582 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:18,582 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:18,586 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:18,587 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:18,665 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:18,676 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148638673
2025-08-14 13:17:18,679 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:18,681 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:18,683 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:18,685 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148638673
2025-08-14 13:17:18,686 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:19,345 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:19,345 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:19,350 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:19,350 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:19,350 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:19,355 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:19,355 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:19,362 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:19,362 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:19,362 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:19,362 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:19,363 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:19,363 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:19,363 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:19,366 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:19,368 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:19,455 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:19,468 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148639463
2025-08-14 13:17:19,471 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:19,472 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:19,475 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:19,477 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148639463
2025-08-14 13:17:19,479 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:20,130 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:20,130 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:20,135 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:20,136 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:20,136 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:20,141 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:20,141 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:20,145 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:20,145 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:20,146 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:20,146 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:20,146 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:20,146 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:20,146 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:20,149 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:20,150 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:20,241 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:20,252 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148640248
2025-08-14 13:17:20,255 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:20,256 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:20,259 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:20,262 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148640248
2025-08-14 13:17:20,263 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:20,709 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:20,710 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:20,718 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:20,719 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:20,719 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:20,725 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:20,725 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:20,729 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:20,730 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:20,730 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:20,730 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:20,730 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:20,730 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:20,730 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:20,735 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:20,736 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:20,820 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:20,830 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148640826
2025-08-14 13:17:20,834 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:20,835 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:20,838 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:20,840 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148640826
2025-08-14 13:17:20,842 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:21,114 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:21,114 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:21,117 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:21,117 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:21,117 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:21,123 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:21,123 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:21,128 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:21,128 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:21,128 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:21,128 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:21,128 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:21,128 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:21,128 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:21,132 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:21,133 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:21,214 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:21,225 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148641222
2025-08-14 13:17:21,228 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:21,229 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:21,233 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:21,236 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148641222
2025-08-14 13:17:21,238 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:21,463 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:21,463 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:21,466 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:21,466 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:21,466 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:21,473 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:21,474 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:21,477 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:21,477 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:21,477 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:21,478 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:21,478 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:21,478 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:21,478 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:21,481 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:21,482 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:21,558 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:21,570 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148641564
2025-08-14 13:17:21,574 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:21,575 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:21,578 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:21,581 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148641564
2025-08-14 13:17:21,582 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:21,979 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:21,979 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:21,982 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:21,982 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:21,982 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:21,989 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:21,989 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:21,992 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:21,992 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:21,993 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:21,993 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:21,993 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:21,993 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:21,993 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:21,997 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:21,998 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:22,089 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:22,100 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148642096
2025-08-14 13:17:22,103 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:22,105 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:22,107 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:22,110 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148642096
2025-08-14 13:17:22,112 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:22,343 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:22,343 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:22,346 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:22,346 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:22,346 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:22,350 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:22,350 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:22,353 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:22,354 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:22,354 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:22,354 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:22,354 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:22,354 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:22,354 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:22,358 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:22,359 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:22,437 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:22,449 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148642444
2025-08-14 13:17:22,451 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:22,453 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:22,455 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:22,457 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148642444
2025-08-14 13:17:22,459 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:22,677 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:22,677 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:22,681 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:22,681 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:22,681 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:22,685 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:22,685 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:22,688 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:22,688 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:22,689 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:22,689 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:22,689 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:22,689 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:22,689 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:22,692 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:22,693 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:22,766 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:22,776 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148642772
2025-08-14 13:17:22,779 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:22,780 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:22,783 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:22,786 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148642772
2025-08-14 13:17:22,788 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:22,859 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:22,859 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:22,862 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:22,862 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:22,862 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:22,866 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:22,866 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:22,869 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:22,869 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:22,869 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:22,869 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:22,870 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:22,870 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:22,870 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:22,873 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:22,874 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:22,950 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:22,960 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148642956
2025-08-14 13:17:22,962 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:22,964 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:22,967 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:22,969 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148642956
2025-08-14 13:17:22,970 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:23,046 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:23,046 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:23,049 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:23,049 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:23,049 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:23,053 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:23,053 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:23,057 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:23,057 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:23,057 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:23,057 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:23,057 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:23,057 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:23,057 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:23,060 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:23,061 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:23,137 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:23,148 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148643144
2025-08-14 13:17:23,152 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:23,153 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:23,156 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:23,158 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148643144
2025-08-14 13:17:23,160 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:17:23,373 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:23,374 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:17:23,376 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:17:23,376 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:17:23,376 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:17:23,384 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:17:23,384 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:17:23,389 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:17:23,389 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:17:23,389 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:17:23,389 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:17:23,389 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:17:23,389 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:17:23,390 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:17:23,394 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:17:23,395 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:17:23,468 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:17:23,478 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148643474
2025-08-14 13:17:23,481 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:17:23,482 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:17:23,485 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:17:23,487 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148643474
2025-08-14 13:17:23,489 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:18:28,936 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 13:18:30,326 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 13:18:35,552 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:35,552 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:18:35,567 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:35,567 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:18:35,588 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitService [L:154] - 通用API访问记录 - IP: 0:0:***, API: /bms/auth/login, 当前次数: 1/3
2025-08-14 13:18:35,589 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:18:35,658 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:18:35,658 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:18:35,711 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:18:35,711 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:18:35,712 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:18:35,712 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:18:35,712 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:18:35,712 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:18:35,712 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:18:35,722 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:18:35,724 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:18:35,797 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:18:35,838 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148715832
2025-08-14 13:18:35,841 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:18:35,842 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:18:35,847 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:18:35,851 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148715832
2025-08-14 13:18:35,853 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:18:36,058 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:36,058 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:18:36,062 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:36,063 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:18:36,068 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitService [L:154] - 通用API访问记录 - IP: 0:0:***, API: /bms/auth/login, 当前次数: 2/3
2025-08-14 13:18:36,069 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:18:36,073 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:18:36,073 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:18:36,078 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:18:36,078 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:18:36,079 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:18:36,079 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:18:36,079 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:18:36,079 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:18:36,079 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:18:36,083 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:18:36,085 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:18:36,154 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:18:36,166 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148716162
2025-08-14 13:18:36,170 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:18:36,172 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:18:36,176 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:18:36,179 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148716162
2025-08-14 13:18:36,181 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:18:36,642 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:36,642 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:18:36,653 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:36,653 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:18:36,657 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitService [L:154] - 通用API访问记录 - IP: 0:0:***, API: /bms/auth/login, 当前次数: 3/3
2025-08-14 13:18:36,657 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:18:36,662 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:18:36,662 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:18:36,669 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:18:36,669 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:18:36,669 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:18:36,669 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:18:36,669 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:18:36,669 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:18:36,669 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:18:36,673 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:18:36,674 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:18:36,765 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:18:36,780 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755148716774
2025-08-14 13:18:36,785 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:18:36,787 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:18:36,791 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:18:36,794 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755148716774
2025-08-14 13:18:36,796 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:18:37,241 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:37,242 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:18:37,247 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:37,248 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:18:37,805 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:37,806 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:18:37,811 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:37,812 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:18:37,989 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:37,990 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:18:37,994 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:37,994 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:18:38,175 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,175 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:18:38,178 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,178 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:18:38,359 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,359 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:18:38,363 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,364 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:18:38,549 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,549 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:18:38,552 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,552 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:18:38,724 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,725 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:18:38,729 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:18:38,730 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:34:42,217 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 13:34:43,535 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 13:35:26,602 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:26,603 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:35:26,619 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:26,619 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:35:26,620 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:35:26,695 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:35:26,696 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:35:26,746 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:35:26,746 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:35:26,746 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:35:26,746 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:35:26,746 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:35:26,746 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:35:26,746 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:35:26,760 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:35:26,763 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:35:26,834 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:35:26,881 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755149726875
2025-08-14 13:35:26,885 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:35:26,887 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:35:26,890 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:35:26,894 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755149726875
2025-08-14 13:35:26,897 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:35:27,587 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:27,588 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:35:27,592 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:27,592 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:35:27,592 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:35:27,598 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:35:27,598 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:35:27,604 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:35:27,604 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:35:27,604 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:35:27,604 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:35:27,604 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:35:27,604 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:35:27,605 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:35:27,608 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:35:27,609 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:35:27,690 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:35:27,701 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755149727698
2025-08-14 13:35:27,704 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:35:27,705 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:35:27,708 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:35:27,711 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755149727698
2025-08-14 13:35:27,713 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:35:28,462 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:28,463 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:35:28,469 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:28,469 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:35:28,469 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:35:28,474 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:35:28,474 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:35:28,481 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:35:28,482 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:35:28,482 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:35:28,482 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:35:28,482 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:35:28,483 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:35:28,483 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:35:28,487 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:35:28,488 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:35:28,577 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:35:28,589 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755149728585
2025-08-14 13:35:28,591 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:35:28,593 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:35:28,595 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:35:28,601 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755149728585
2025-08-14 13:35:28,603 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:35:29,050 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:29,050 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:35:29,054 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:29,054 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:35:29,054 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:35:29,058 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:35:29,058 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:35:29,062 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:35:29,062 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:35:29,063 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:35:29,063 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:35:29,063 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:35:29,063 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:35:29,063 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:35:29,067 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:35:29,068 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:35:29,154 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:35:29,168 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755149729164
2025-08-14 13:35:29,172 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:35:29,174 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:35:29,176 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:35:29,178 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755149729164
2025-08-14 13:35:29,179 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:35:29,550 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:29,550 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:35:29,564 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:29,564 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:35:29,565 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:35:29,571 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:35:29,571 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:35:29,577 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:35:29,577 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:35:29,577 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:35:29,577 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:35:29,577 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:35:29,578 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:35:29,578 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:35:29,582 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:35:29,583 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:35:29,670 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:35:29,679 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755149729676
2025-08-14 13:35:29,681 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:35:29,682 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:35:29,686 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:35:29,689 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755149729676
2025-08-14 13:35:29,691 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:35:30,361 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:30,361 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:35:30,371 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:30,371 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:35:30,371 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:35:30,376 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:35:30,376 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:35:30,382 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:35:30,382 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:35:30,382 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:35:30,382 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:35:30,382 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:35:30,383 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:35:30,383 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:35:30,387 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:35:30,388 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:35:30,475 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:35:30,485 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755149730482
2025-08-14 13:35:30,489 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:35:30,491 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:35:30,494 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:35:30,496 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755149730482
2025-08-14 13:35:30,499 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:35:30,926 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:30,926 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:35:30,943 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:30,943 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:35:30,943 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:35:30,948 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:35:30,949 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:35:30,953 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:35:30,953 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:35:30,954 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:35:30,954 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:35:30,954 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:35:30,954 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:35:30,954 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:35:30,957 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:35:30,958 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:35:31,046 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:35:31,056 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755149731052
2025-08-14 13:35:31,058 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:35:31,060 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:35:31,064 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:35:31,066 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755149731052
2025-08-14 13:35:31,068 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:35:31,714 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:31,715 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 13:35:31,724 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 13:35:31,724 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 13:35:31,724 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 13:35:31,729 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 13:35:31,729 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 13:35:31,735 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 13:35:31,735 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 13:35:31,735 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 13:35:31,736 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 13:35:31,736 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 13:35:31,736 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 13:35:31,736 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 13:35:31,740 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 13:35:31,741 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 13:35:31,828 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 13:35:31,842 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755149731837
2025-08-14 13:35:31,844 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 13:35:31,845 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 13:35:31,848 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 13:35:31,851 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755149731837
2025-08-14 13:35:31,852 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 13:35:39,179 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:39,180 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:35:39,555 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:39,555 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:35:39,999 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:39,999 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:35:40,152 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:40,153 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:35:40,355 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:40,356 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:35:40,536 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:40,536 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:35:40,716 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:40,716 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:35:40,894 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:40,895 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:35:41,073 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:41,073 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:35:41,281 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:41,281 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:35:41,456 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 13:35:41,456 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 13:36:56,889 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 13:36:58,207 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 14:37:53,144 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-14 14:37:54,612 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-14 14:38:02,928 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 14:38:02,929 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-14 14:38:02,943 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-14 14:38:02,944 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-14 14:38:02,944 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-14 14:38:03,009 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 14:38:03,009 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 14:38:03,055 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 14:38:03,056 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 14:38:03,056 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 14:38:03,056 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 14:38:03,056 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 14:38:03,056 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 14:38:03,057 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 14:38:03,072 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 14:38:03,074 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 14:38:03,149 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-14 14:38:03,198 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755153483191
2025-08-14 14:38:03,200 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 14:38:03,202 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 14:38:03,206 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 14:38:03,208 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755153483191
2025-08-14 14:38:03,218 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 14:38:54,394 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/refresh from IP: 0:0:***
2025-08-14 14:38:54,395 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/refresh
2025-08-14 14:38:54,400 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/refresh from IP: 0:0:***
2025-08-14 14:38:54,400 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/refresh
2025-08-14 14:38:54,404 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitService [L:154] - 通用API访问记录 - IP: 0:0:***, API: /bms/auth/refresh, 当前次数: 1/3
2025-08-14 14:38:54,404 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/refresh
2025-08-14 14:39:11,848 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/refresh from IP: 0:0:***
2025-08-14 14:39:11,850 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/refresh
2025-08-14 14:39:11,858 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitService [L:154] - 通用API访问记录 - IP: 0:0:***, API: /bms/auth/refresh, 当前次数: 2/3
2025-08-14 14:39:11,858 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-6] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/refresh
2025-08-14 14:39:59,584 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/refresh from IP: 0:0:***
2025-08-14 14:39:59,585 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/refresh
2025-08-14 14:39:59,591 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitService [L:154] - 通用API访问记录 - IP: 0:0:***, API: /bms/auth/refresh, 当前次数: 3/3
2025-08-14 14:39:59,592 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/refresh
2025-08-14 14:39:59,610 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:351] - BMS JWT Refresh Token验证成功: username=admin
2025-08-14 14:39:59,610 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-14 14:39:59,610 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-14 14:39:59,617 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-14 14:39:59,617 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-14 14:39:59,617 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-14 14:39:59,618 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-14 14:39:59,618 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-14 14:39:59,618 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-14 14:39:59,618 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-14 14:39:59,621 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-14 14:39:59,622 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-14 14:39:59,633 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755153599627
2025-08-14 14:39:59,636 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-14 14:39:59,637 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-14 14:39:59,640 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-14 14:39:59,642 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755153599627
2025-08-14 14:39:59,644 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-14 14:39:59,647 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:379] - Refresh Token失效成功
2025-08-14 14:40:40,868 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 14:40:40,868 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 14:40:40,881 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.TokenManagementService [L:168] - Token在Redis中不存在: eyJ0eXAiOiJKV1QiLCJh
2025-08-14 14:40:40,882 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.JwtAuthenticationFilter [L:66] - Token在Redis中不存在或已失效
2025-08-14 14:40:40,883 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 14:40:40,883 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-14 14:40:42,202 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:168] - Token在Redis中不存在: eyJ0eXAiOiJKV1QiLCJh
2025-08-14 14:40:42,203 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.JwtAuthenticationFilter [L:66] - Token在Redis中不存在或已失效
2025-08-14 14:40:42,204 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-14 14:40:42,205 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
