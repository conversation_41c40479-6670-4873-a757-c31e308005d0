2025-08-15 15:54:38,796 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 70617 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-15 15:54:38,808 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-15 15:54:39,515 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-15 15:54:39,517 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-15 15:54:39,556 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 33ms. Found 0 repository interfaces.
2025-08-15 15:54:39,817 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$7aff16c1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 15:54:39,953 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-15 15:54:39,961 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-15 15:54:39,966 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-15 15:54:39,966 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-15 15:54:40,022 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-15 15:54:40,022 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1193 ms
2025-08-15 15:54:40,328 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-15 15:54:40,443 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-15 15:54:40,443 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-15 15:54:41,371 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - === 开发环境安全配置 ===
2025-08-15 15:54:41,371 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 当前环境: [uat]
2025-08-15 15:54:41,371 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 认证模式: 开发模式（无需认证）⚠️
2025-08-15 15:54:41,371 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - 数据库认证: 启用（仅用于登录接口）
2025-08-15 15:54:41,371 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - 详细认证日志: 启用
2025-08-15 15:54:41,372 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:55] - =======================
2025-08-15 15:54:41,438 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 46 ms to scan 5 urls, producing 133 keys and 488 values 
2025-08-15 15:54:41,514 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:99] - BMS JWT工具类初始化完成
2025-08-15 15:54:41,514 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:100] - Access Token有效期: 24 小时
2025-08-15 15:54:41,514 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:101] - Refresh Token有效期: 48 小时
2025-08-15 15:54:41,514 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:102] - 发行人: CATHAY-FUTURE-BMS-SYSTEM
2025-08-15 15:54:41,514 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:103] - 受众: CATHAY-FUTURE-BMS-SYSTEM
2025-08-15 15:54:41,617 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:77] - 配置数据库认证提供者（开发环境，启用详细日志）
2025-08-15 15:54:41,623 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:29] - LoggingAuthenticationProvider 初始化完成
2025-08-15 15:54:41,623 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:30] - UserDetailsService: DatabaseUserDetailsService
2025-08-15 15:54:41,623 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:31] - PasswordEncoder: BCryptPasswordEncoder
2025-08-15 15:54:41,660 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1f48fa72, org.springframework.security.web.context.SecurityContextPersistenceFilter@566f4659, org.springframework.security.web.header.HeaderWriterFilter@579f3c8e, org.springframework.security.web.authentication.logout.LogoutFilter@2b4829aa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7b8b07ae, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@39514976, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@35b3c1f6, org.springframework.security.web.session.SessionManagementFilter@2dd8a273, org.springframework.security.web.access.ExceptionTranslationFilter@521a506c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@42d174ad]
2025-08-15 15:54:41,753 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-15 15:54:42,047 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-15 15:54:42,115 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-15 15:54:42,122 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-15 15:54:42,124 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.657 seconds (JVM running for 3.976)
2025-08-15 15:54:42,491 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-15 15:54:42,494 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-15 15:54:42,495 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-15 15:54:42,501 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 6 ms
2025-08-15 15:54:42,646 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-15 15:54:46,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-15 15:54:46,801 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-15 15:54:46,809 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-15 16:10:26,741 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 72389 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-15 16:10:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-15 16:10:27,603 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-15 16:10:27,604 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-15 16:10:27,649 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 36ms. Found 0 repository interfaces.
2025-08-15 16:10:27,934 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b133094a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 16:10:28,096 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-15 16:10:28,105 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-15 16:10:28,111 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-15 16:10:28,111 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-15 16:10:28,183 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-15 16:10:28,184 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1396 ms
2025-08-15 16:10:28,551 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-15 16:10:28,701 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for ***********/***********:6379
2025-08-15 16:10:28,701 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for ***********/***********:6379
2025-08-15 16:10:29,753 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - === 开发环境安全配置 ===
2025-08-15 16:10:29,754 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 当前环境: [test]
2025-08-15 16:10:29,754 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 认证模式: 开发模式（无需认证）⚠️
2025-08-15 16:10:29,754 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - 数据库认证: 启用（仅用于登录接口）
2025-08-15 16:10:29,754 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - 详细认证日志: 禁用
2025-08-15 16:10:29,754 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:55] - =======================
2025-08-15 16:10:29,828 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 50 ms to scan 5 urls, producing 133 keys and 488 values 
2025-08-15 16:10:29,902 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:99] - BMS JWT工具类初始化完成
2025-08-15 16:10:29,902 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:100] - Access Token有效期: 24 小时
2025-08-15 16:10:29,902 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:101] - Refresh Token有效期: 48 小时
2025-08-15 16:10:29,902 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:102] - 发行人: CATHAY-FUTURE-BMS-SYSTEM
2025-08-15 16:10:29,902 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:103] - 受众: CATHAY-FUTURE-BMS-SYSTEM
2025-08-15 16:10:30,022 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:81] - 配置数据库认证提供者（开发环境，标准模式）
2025-08-15 16:10:30,071 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5ddd84d2, org.springframework.security.web.context.SecurityContextPersistenceFilter@768f4b42, org.springframework.security.web.header.HeaderWriterFilter@6921cfa, org.springframework.security.web.authentication.logout.LogoutFilter@7d18338b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@63b4b9c6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@d4ecdb0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3fd5d679, org.springframework.security.web.session.SessionManagementFilter@5fa5c8cf, org.springframework.security.web.access.ExceptionTranslationFilter@592cb470, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@22899683]
2025-08-15 16:10:30,165 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-15 16:10:30,461 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-15 16:10:30,526 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-15 16:10:30,533 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-15 16:10:30,535 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 4.196 seconds (JVM running for 4.713)
2025-08-15 16:10:31,141 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-15 16:10:31,143 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-15 16:10:31,143 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-15 16:10:31,159 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 16 ms
2025-08-15 16:10:31,377 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(2)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-15 16:11:45,066 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-15 16:11:45,109 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-15 16:11:45,113 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
