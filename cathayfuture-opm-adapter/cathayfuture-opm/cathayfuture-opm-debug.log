2025-08-13 09:40:28,594 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 09:40:31,075 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 09:41:19,219 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 09:41:21,650 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 09:47:17,996 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 09:47:20,292 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 11:03:02,555 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 11:03:04,936 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 11:03:32,579 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:63] - 密码哈希长度: 60
2025-08-13 11:03:32,580 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:64] - 密码哈希格式: $2a$10$FxZ
2025-08-13 11:03:32,587 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:146] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-13 11:03:32,726 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:58] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-13 11:03:32,733 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:201] - JWT Refresh Token生成成功: username=admin
2025-08-13 11:04:17,131 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.TokenManagementService [L:87] - Token验证成功: username=admin
2025-08-13 11:04:17,149 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] c.o.a.s.JwtAuthenticationFilter [L:57] - JWT认证成功，用户: ad***n, 权限数量: 1
2025-08-13 11:04:47,171 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:87] - Token验证成功: username=admin
2025-08-13 11:04:47,172 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.JwtAuthenticationFilter [L:57] - JWT认证成功，用户: ad***n, 权限数量: 1
2025-08-13 11:05:48,545 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.TokenManagementService [L:129] - 删除用户token映射: username=admin
2025-08-13 11:06:02,921 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:79] - Token在Redis中不存在: eyJ0eXAiOiJKV1QiLCJh
2025-08-13 11:06:02,921 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.JwtAuthenticationFilter [L:66] - Token在Redis中不存在或已失效
2025-08-13 17:10:11,566 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 17:10:12,651 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:13:16,113 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 17:13:17,202 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:15:37,062 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 17:15:38,107 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:19:23,632 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 17:19:24,683 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:22:20,782 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:46] - === 开始数据库用户认证流程 ===
2025-08-13 17:22:20,784 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:47] - 请求认证的用户名: ad***n
2025-08-13 17:22:20,914 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:56] - ✅ 找到用户: ad***n (ID: ***)
2025-08-13 17:22:20,914 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:57] - 用户编码: ADMIN001
2025-08-13 17:22:20,914 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:58] - 真实姓名: 系**
2025-08-13 17:22:20,914 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:59] - OA姓名: 系**
2025-08-13 17:22:20,914 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:60] - 用户状态: 1
2025-08-13 17:22:20,914 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:64] - 密码哈希长度: 60
2025-08-13 17:22:20,914 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:65] - 密码哈希格式: $2a$10$FxZ
2025-08-13 17:22:20,919 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:147] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-13 17:22:20,922 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:124] - === DatabaseUserDetailsService处理完成 ===
2025-08-13 17:22:21,003 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:46] - === 开始数据库用户认证流程 ===
2025-08-13 17:22:21,003 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:47] - 请求认证的用户名: ad***n
2025-08-13 17:22:21,008 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:56] - ✅ 找到用户: ad***n (ID: ***)
2025-08-13 17:22:21,008 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:57] - 用户编码: ADMIN001
2025-08-13 17:22:21,008 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:58] - 真实姓名: 系**
2025-08-13 17:22:21,008 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:59] - OA姓名: 系**
2025-08-13 17:22:21,008 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:60] - 用户状态: 1
2025-08-13 17:22:21,008 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:64] - 密码哈希长度: 60
2025-08-13 17:22:21,008 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:65] - 密码哈希格式: $2a$10$FxZ
2025-08-13 17:22:21,011 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:147] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-13 17:22:21,012 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:124] - === DatabaseUserDetailsService处理完成 ===
2025-08-13 17:22:21,099 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.security.LoginRateLimiter [L:153] - IP 0:0:*** 登录失败次数: 1/100
2025-08-13 17:22:21,102 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.security.LoginRateLimiter [L:174] - 用户名 ad***n 登录失败次数: 1/50
2025-08-13 17:23:26,749 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:46] - === 开始数据库用户认证流程 ===
2025-08-13 17:23:26,749 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:47] - 请求认证的用户名: ad***n
2025-08-13 17:23:26,755 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:56] - ✅ 找到用户: ad***n (ID: ***)
2025-08-13 17:23:26,755 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:57] - 用户编码: ADMIN001
2025-08-13 17:23:26,755 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:58] - 真实姓名: 系**
2025-08-13 17:23:26,755 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:59] - OA姓名: 系**
2025-08-13 17:23:26,755 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:60] - 用户状态: 1
2025-08-13 17:23:26,755 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:64] - 密码哈希长度: 60
2025-08-13 17:23:26,755 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:65] - 密码哈希格式: $2a$10$FxZ
2025-08-13 17:23:26,760 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:147] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-13 17:23:26,761 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:124] - === DatabaseUserDetailsService处理完成 ===
2025-08-13 17:23:26,844 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-13 17:23:26,871 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755077006867
2025-08-13 17:23:26,873 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-13 17:23:26,874 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-13 17:23:26,878 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-13 17:23:26,881 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755077006867
2025-08-13 17:23:26,883 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-13 17:23:39,425 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.TokenManagementService [L:176] - Token验证成功: username=admin
2025-08-13 17:23:39,431 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-4] c.o.a.s.JwtAuthenticationFilter [L:57] - JWT认证成功，用户: ad***n, 权限数量: 1
2025-08-13 17:26:29,899 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-13 17:26:30,952 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-13 17:27:29,168 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:46] - === 开始数据库用户认证流程 ===
2025-08-13 17:27:29,169 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:47] - 请求认证的用户名: ad***n
2025-08-13 17:27:29,279 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:56] - ✅ 找到用户: ad***n (ID: ***)
2025-08-13 17:27:29,279 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:57] - 用户编码: ADMIN001
2025-08-13 17:27:29,280 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:58] - 真实姓名: 系**
2025-08-13 17:27:29,280 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:59] - OA姓名: 系**
2025-08-13 17:27:29,280 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:60] - 用户状态: 1
2025-08-13 17:27:29,280 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:64] - 密码哈希长度: 60
2025-08-13 17:27:29,280 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:65] - 密码哈希格式: $2a$10$FxZ
2025-08-13 17:27:29,285 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:147] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-13 17:27:29,286 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:124] - === DatabaseUserDetailsService处理完成 ===
2025-08-13 17:27:29,367 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-13 17:27:29,392 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:75] - 获取登录锁成功: username=admin, lockValue=1755077249388
2025-08-13 17:27:29,397 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:279] - 静默删除用户token: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-13 17:27:29,398 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:101] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-13 17:27:29,401 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:118] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-13 17:27:29,403 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:145] - 释放登录锁成功: username=admin, lockValue=1755077249388
2025-08-13 17:27:29,406 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.TokenManagementService [L:320] - JWT Refresh Token生成成功: username=admin
2025-08-13 17:28:12,625 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.opm.adapter.security.BmsJwtUtils [L:267] - BMS Access Token验证失败: The input is not a valid base 64 encoded string.
2025-08-13 17:28:12,626 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.JwtAuthenticationFilter [L:69] - JWT token签名验证失败
2025-08-13 17:28:53,032 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.TokenManagementService [L:176] - Token验证成功: username=admin
2025-08-13 17:28:53,042 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.JwtAuthenticationFilter [L:57] - JWT认证成功，用户: ad***n, 权限数量: 1
2025-08-13 17:29:47,240 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.TokenManagementService [L:176] - Token验证成功: username=admin
2025-08-13 17:29:47,241 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-7] c.o.a.s.JwtAuthenticationFilter [L:57] - JWT认证成功，用户: ad***n, 权限数量: 1
2025-08-13 17:30:47,798 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:176] - Token验证成功: username=admin
2025-08-13 17:30:47,799 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.JwtAuthenticationFilter [L:57] - JWT认证成功，用户: ad***n, 权限数量: 1
