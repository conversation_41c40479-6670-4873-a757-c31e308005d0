2025-08-13 09:40:28,588 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App v1.0-SNAPSHOT on chengzhx-MBP with PID 31393 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/cathayfuture-opm.jar started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 09:40:28,595 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 09:40:30,101 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 09:40:30,102 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 09:40:30,152 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 38ms. Found 0 repository interfaces.
2025-08-13 09:40:30,495 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f7afd8f4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 09:40:30,729 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 09:40:30,742 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 09:40:30,749 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 09:40:30,749 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 09:40:30,819 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 09:40:30,819 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 2169 ms
2025-08-13 09:40:31,774 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 09:40:32,013 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 09:40:32,013 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 09:40:32,901 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 62 ms to scan 5 urls, producing 128 keys and 454 values 
2025-08-13 09:40:33,200 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-13 09:40:33,213 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 09:40:33,214 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 09:40:33,214 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 09:40:33,289 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3a588b5f, org.springframework.security.web.context.SecurityContextPersistenceFilter@18026052, org.springframework.security.web.header.HeaderWriterFilter@37b1149b, org.springframework.security.web.authentication.logout.LogoutFilter@6c6c2a73, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6e068ac9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@62058742, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@20b54cfe, org.springframework.security.web.session.SessionManagementFilter@710ae6a7, org.springframework.security.web.access.ExceptionTranslationFilter@2c8a445b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5409dfdd]
2025-08-13 09:40:33,522 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 09:40:33,890 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 09:40:33,968 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 09:40:33,979 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 09:40:33,981 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 6.057 seconds (JVM running for 6.532)
2025-08-13 09:41:19,217 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App v1.0-SNAPSHOT on chengzhx-MBP with PID 31745 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/cathayfuture-opm.jar started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 09:41:19,220 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-13 09:41:20,557 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 09:41:20,559 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 09:41:20,607 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 39ms. Found 0 repository interfaces.
2025-08-13 09:41:20,980 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$5fa1cd84] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 09:41:21,237 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 09:41:21,271 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 09:41:21,279 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 09:41:21,279 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 09:41:21,354 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 09:41:21,355 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 2097 ms
2025-08-13 09:41:22,353 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 09:41:22,546 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-13 09:41:22,546 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-13 09:41:23,350 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 62 ms to scan 5 urls, producing 128 keys and 454 values 
2025-08-13 09:41:23,669 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-13 09:41:23,684 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 09:41:23,684 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 09:41:23,685 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 09:41:23,775 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@36b53f08, org.springframework.security.web.context.SecurityContextPersistenceFilter@1c7f6e96, org.springframework.security.web.header.HeaderWriterFilter@36dafa24, org.springframework.security.web.authentication.logout.LogoutFilter@892af0e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1ac0cdb0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4cecc15a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@54329480, org.springframework.security.web.session.SessionManagementFilter@2ba024cb, org.springframework.security.web.access.ExceptionTranslationFilter@6d421fe, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4d25f816]
2025-08-13 09:41:24,023 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 09:41:24,437 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 09:41:24,505 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 09:41:24,517 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 09:41:24,519 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 5.96 seconds (JVM running for 6.323)
2025-08-13 09:47:17,994 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App v1.0-SNAPSHOT on chengzhx-MBP with PID 33207 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/cathayfuture-opm.jar started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 09:47:17,996 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-13 09:47:19,280 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 09:47:19,281 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 09:47:19,323 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 33ms. Found 0 repository interfaces.
2025-08-13 09:47:19,676 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$e7e093c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 09:47:19,924 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 09:47:19,938 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 09:47:19,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 09:47:19,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 09:47:20,015 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 09:47:20,015 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1986 ms
2025-08-13 09:47:20,990 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 09:47:21,196 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-13 09:47:21,196 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-13 09:47:21,979 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 64 ms to scan 5 urls, producing 127 keys and 453 values 
2025-08-13 09:47:22,411 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:83] - 配置数据库认证提供者
2025-08-13 09:47:22,425 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 09:47:22,425 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 09:47:22,426 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 09:47:22,505 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@74960e9d, org.springframework.security.web.context.SecurityContextPersistenceFilter@7e3236d, org.springframework.security.web.header.HeaderWriterFilter@11b5f4e2, org.springframework.security.web.authentication.logout.LogoutFilter@44b940a2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@14c99bf6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7fe8c7db, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4001d8c1, org.springframework.security.web.session.SessionManagementFilter@5efe47fd, org.springframework.security.web.access.ExceptionTranslationFilter@3ebc955b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@423ed3b5]
2025-08-13 09:47:22,734 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 09:47:23,133 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 09:47:23,224 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 09:47:23,237 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 09:47:23,239 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 5.867 seconds (JVM running for 6.23)
2025-08-13 09:48:17,011 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 11:03:02,551 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App v1.0-SNAPSHOT on chengzhx-MBP with PID 44872 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/cathayfuture-opm.jar started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 11:03:02,555 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-13 11:03:03,883 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 11:03:03,885 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 11:03:03,935 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 41ms. Found 0 repository interfaces.
2025-08-13 11:03:04,289 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d295b9fe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 11:03:04,558 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 11:03:04,572 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 11:03:04,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 11:03:04,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 11:03:04,654 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 11:03:04,654 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 2062 ms
2025-08-13 11:03:05,648 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 11:03:05,872 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-13 11:03:05,872 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:6379
2025-08-13 11:03:06,632 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:88] - === 安全配置信息 ===
2025-08-13 11:03:06,633 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:89] - 开发模式: 禁用 ✅
2025-08-13 11:03:06,633 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:90] - 数据库认证: 启用
2025-08-13 11:03:06,633 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:91] - 当前环境: [test]
2025-08-13 11:03:06,633 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:96] - ✅ 安全模式：正常的JWT认证已启用
2025-08-13 11:03:06,633 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:98] - ===================
2025-08-13 11:03:06,749 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 59 ms to scan 5 urls, producing 127 keys and 456 values 
2025-08-13 11:03:06,969 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:151] - 配置数据库认证提供者
2025-08-13 11:03:06,983 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 11:03:06,983 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 11:03:06,983 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 11:03:07,034 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:119] - ✅ 配置生产模式安全策略：启用JWT认证
2025-08-13 11:03:07,038 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:140] - ✅ 生产模式安全配置已生效
2025-08-13 11:03:07,060 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5bda157e, org.springframework.security.web.context.SecurityContextPersistenceFilter@7db40fd5, org.springframework.security.web.header.HeaderWriterFilter@14c99bf6, org.springframework.security.web.authentication.logout.LogoutFilter@4844930a, cathayfuture.opm.adapter.security.JwtAuthenticationFilter@2d9dff65, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@31b0f02, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6ffd4c0d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@67e0fd6d, org.springframework.security.web.session.SessionManagementFilter@541afb85, org.springframework.security.web.access.ExceptionTranslationFilter@7e3236d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@486e9d1d]
2025-08-13 11:03:07,292 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 11:03:07,674 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 11:03:07,739 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 11:03:07,751 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 11:03:07,753 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 5.944 seconds (JVM running for 6.348)
2025-08-13 11:03:32,210 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 11:03:32,210 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 11:03:32,217 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 7 ms
2025-08-13 11:03:32,306 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-13 11:03:32,307 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-13 11:03:32,307 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-13 11:03:32,307 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-13 11:03:32,381 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:45] - === 开始数据库用户认证流程 ===
2025-08-13 11:03:32,382 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:46] - 请求认证的用户名: admin
2025-08-13 11:03:32,432 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-13 11:03:32,543 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-13 11:03:32,578 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:55] - ✅ 找到用户: admin (ID: 1)
2025-08-13 11:03:32,578 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:56] - 用户编码: ADMIN001
2025-08-13 11:03:32,578 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:57] - 真实姓名: 系统管理员
2025-08-13 11:03:32,578 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:58] - OA姓名: 系统管理员
2025-08-13 11:03:32,579 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户状态: 1
2025-08-13 11:03:32,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:70] - === 检查用户状态 ===
2025-08-13 11:03:32,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:72] - 用户是否启用: true
2025-08-13 11:03:32,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:80] - 账户是否锁定: false
2025-08-13 11:03:32,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:81] - 账户锁定时间: null
2025-08-13 11:03:32,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:88] - 密码是否过期: false
2025-08-13 11:03:32,580 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:89] - 密码更新时间: 2025-08-12T14:02:18
2025-08-13 11:03:32,581 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:95] - === 加载用户权限 ===
2025-08-13 11:03:32,587 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:100] - 用户权限列表: [ROLE_ADMIN]
2025-08-13 11:03:32,587 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:101] - 权限数量: 1
2025-08-13 11:03:32,587 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:104] - === 构建UserDetails对象 ===
2025-08-13 11:03:32,587 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:105] - 用户名: admin
2025-08-13 11:03:32,587 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:106] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 11:03:32,587 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:107] - 账户过期: false
2025-08-13 11:03:32,587 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:108] - 账户锁定: false
2025-08-13 11:03:32,588 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:109] - 凭据过期: false
2025-08-13 11:03:32,588 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:110] - 账户禁用: false
2025-08-13 11:03:32,589 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:122] - ✅ UserDetails构建完成，返回给Spring Security进行密码验证
2025-08-13 11:03:32,589 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:123] - === DatabaseUserDetailsService处理完成 ===
2025-08-13 11:03:32,589 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-13 11:03:32,589 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-13 11:03:32,589 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-13 11:03:32,589 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 11:03:32,590 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-13 11:03:32,590 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$FxZ
2025-08-13 11:03:32,590 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-13 11:03:32,590 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-13 11:03:32,660 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: true
2025-08-13 11:03:32,661 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:109] - ✅ 密码验证成功
2025-08-13 11:03:32,661 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:45] - ✅ 认证成功: admin
2025-08-13 11:03:32,661 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.LoggingAuthenticationProvider [L:46] - 认证结果权限: [ROLE_ADMIN]
2025-08-13 11:05:48,548 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.s.TokenManagementService [L:135] - Token失效成功: token前缀=eyJ0eXAiOiJKV1QiLCJh
2025-08-13 16:06:51,584 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 92576 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 16:06:51,586 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:06:52,193 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:06:52,194 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:06:52,229 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 30ms. Found 0 repository interfaces.
2025-08-13 16:06:52,465 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$7edd743e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:06:52,662 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:06:52,671 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:06:52,677 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:06:52,677 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:06:52,724 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:06:52,724 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1122 ms
2025-08-13 16:06:52,805 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:06:52,826 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:08:16,868 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 93070 (/Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter/target/classes started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 16:08:16,870 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:08:17,470 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:08:17,471 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:08:17,504 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 28ms. Found 0 repository interfaces.
2025-08-13 16:08:17,715 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$c14c29d1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:08:17,884 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:08:17,892 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:08:17,897 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:08:17,897 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:08:17,941 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:08:17,941 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1057 ms
2025-08-13 16:08:18,018 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:08:18,030 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:12:41,320 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 94135 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 16:12:41,325 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:12:41,907 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:12:41,907 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:12:41,937 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 26ms. Found 0 repository interfaces.
2025-08-13 16:12:42,120 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$b35319ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:12:42,264 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:12:42,270 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:12:42,276 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:12:42,276 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:12:42,330 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:12:42,330 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 973 ms
2025-08-13 16:12:42,485 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:12:42,494 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 16:14:06,961 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 94819 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 16:14:06,964 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 16:14:07,529 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 16:14:07,529 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 16:14:07,562 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 29ms. Found 0 repository interfaces.
2025-08-13 16:14:07,741 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$654edab6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 16:14:07,866 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 16:14:07,873 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 16:14:07,877 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 16:14:07,877 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 16:14:07,925 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 16:14:07,925 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 951 ms
2025-08-13 16:14:07,993 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 16:14:08,003 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 17:02:14,508 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 355 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 17:02:14,509 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 17:02:15,007 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 17:02:15,008 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 17:02:15,039 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 27ms. Found 0 repository interfaces.
2025-08-13 17:02:15,216 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$76a00bbc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 17:02:15,336 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 17:02:15,343 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 17:02:15,348 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 17:02:15,348 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 17:02:15,402 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 17:02:15,402 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 881 ms
2025-08-13 17:02:15,985 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 17:02:16,071 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:02:16,071 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:02:16,457 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:52] - === 生产环境安全配置 ===
2025-08-13 17:02:16,457 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:53] - 当前环境: [uat]
2025-08-13 17:02:16,457 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:54] - 数据库认证: 启用
2025-08-13 17:02:16,457 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:55] - JWT认证: 启用 ✅
2025-08-13 17:02:16,457 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:56] - =======================
2025-08-13 17:02:16,505 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 32 ms to scan 5 urls, producing 130 keys and 469 values 
2025-08-13 17:02:16,550 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 17:02:16,559 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 17:10:11,565 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 1168 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 17:10:11,566 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 17:10:12,088 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 17:10:12,089 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 17:10:12,118 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 25ms. Found 0 repository interfaces.
2025-08-13 17:10:12,300 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3970ccf3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 17:10:12,421 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 17:10:12,427 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 17:10:12,433 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 17:10:12,433 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 17:10:12,484 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 17:10:12,484 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 905 ms
2025-08-13 17:10:13,067 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 17:10:13,154 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:10:13,154 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:10:13,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:52] - === 生产环境安全配置 ===
2025-08-13 17:10:13,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:53] - 当前环境: [uat]
2025-08-13 17:10:13,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:54] - 数据库认证: 启用
2025-08-13 17:10:13,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:55] - JWT认证: 启用 ✅
2025-08-13 17:10:13,552 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:56] - =======================
2025-08-13 17:10:13,604 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 34 ms to scan 5 urls, producing 130 keys and 469 values 
2025-08-13 17:10:13,649 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 17:10:13,658 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 17:13:16,112 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 1455 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 17:13:16,114 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 17:13:16,634 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 17:13:16,635 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 17:13:16,665 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 26ms. Found 0 repository interfaces.
2025-08-13 17:13:16,848 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$76a00bbc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 17:13:16,970 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 17:13:16,976 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 17:13:16,981 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 17:13:16,982 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 17:13:17,033 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 17:13:17,033 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 907 ms
2025-08-13 17:13:17,633 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 17:13:17,732 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:13:17,732 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:13:18,146 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:52] - === 生产环境安全配置 ===
2025-08-13 17:13:18,146 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:53] - 当前环境: [uat]
2025-08-13 17:13:18,147 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:54] - 数据库认证: 启用
2025-08-13 17:13:18,147 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:55] - JWT认证: 启用 ✅
2025-08-13 17:13:18,147 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:56] - =======================
2025-08-13 17:13:18,199 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 35 ms to scan 5 urls, producing 130 keys and 469 values 
2025-08-13 17:13:18,245 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Stopping service [Tomcat]
2025-08-13 17:13:18,254 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener [L:142] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-13 17:15:37,061 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 1682 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 17:15:37,063 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 17:15:37,556 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 17:15:37,556 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 17:15:37,586 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 26ms. Found 0 repository interfaces.
2025-08-13 17:15:37,762 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$81a4a15a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 17:15:37,881 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 17:15:37,888 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 17:15:37,893 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 17:15:37,893 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 17:15:37,943 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 17:15:37,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 869 ms
2025-08-13 17:15:38,516 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 17:15:38,598 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:15:38,599 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:15:38,987 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:52] - === 生产环境安全配置 ===
2025-08-13 17:15:38,987 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:53] - 当前环境: [uat]
2025-08-13 17:15:38,987 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:54] - 数据库认证: 启用
2025-08-13 17:15:38,987 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:55] - JWT认证: 启用 ✅
2025-08-13 17:15:38,987 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:56] - =======================
2025-08-13 17:15:39,035 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 33 ms to scan 5 urls, producing 130 keys and 468 values 
2025-08-13 17:15:39,075 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:99] - BMS JWT工具类初始化完成
2025-08-13 17:15:39,075 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:100] - Access Token有效期: 24 小时
2025-08-13 17:15:39,075 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:101] - Refresh Token有效期: 48 小时
2025-08-13 17:15:39,075 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:102] - 发行人: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 17:15:39,075 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:103] - 受众: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 17:15:39,174 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:90] - 配置数据库认证提供者（生产环境）
2025-08-13 17:15:39,180 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 17:15:39,180 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 17:15:39,180 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 17:15:39,193 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:61] - ✅ 配置生产环境安全策略：启用JWT认证
2025-08-13 17:15:39,195 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:82] - ✅ 生产环境安全配置已生效
2025-08-13 17:15:39,206 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3dceec83, org.springframework.security.web.context.SecurityContextPersistenceFilter@21d478f3, org.springframework.security.web.header.HeaderWriterFilter@2e7cd4bb, org.springframework.security.web.authentication.logout.LogoutFilter@2e871e93, cathayfuture.opm.adapter.security.JwtAuthenticationFilter@3c98981e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@19ea65f4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3fecdd00, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@74b13aa, org.springframework.security.web.session.SessionManagementFilter@564e9c0b, org.springframework.security.web.access.ExceptionTranslationFilter@5e1535e0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@20a434a8]
2025-08-13 17:15:39,279 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 17:15:39,501 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 17:15:39,555 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 17:15:39,563 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 17:15:39,565 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 2.733 seconds (JVM running for 2.901)
2025-08-13 17:16:04,040 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 17:16:04,041 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 17:16:04,046 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 5 ms
2025-08-13 17:16:53,739 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 17:19:23,630 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 1968 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 17:19:23,632 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 17:19:24,138 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 17:19:24,139 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 17:19:24,170 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 28ms. Found 0 repository interfaces.
2025-08-13 17:19:24,347 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3678c12e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 17:19:24,459 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 17:19:24,466 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 17:19:24,471 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 17:19:24,471 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 17:19:24,520 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 17:19:24,520 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 875 ms
2025-08-13 17:19:25,085 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 17:19:25,166 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:19:25,166 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:19:25,553 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:52] - === 生产环境安全配置 ===
2025-08-13 17:19:25,553 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:53] - 当前环境: [uat]
2025-08-13 17:19:25,553 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:54] - 数据库认证: 启用
2025-08-13 17:19:25,553 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:55] - JWT认证: 启用 ✅
2025-08-13 17:19:25,553 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:56] - =======================
2025-08-13 17:19:25,603 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 33 ms to scan 5 urls, producing 130 keys and 468 values 
2025-08-13 17:19:25,645 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:99] - BMS JWT工具类初始化完成
2025-08-13 17:19:25,645 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:100] - Access Token有效期: 24 小时
2025-08-13 17:19:25,645 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:101] - Refresh Token有效期: 48 小时
2025-08-13 17:19:25,645 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:102] - 发行人: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 17:19:25,645 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:103] - 受众: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 17:19:25,745 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:90] - 配置数据库认证提供者（生产环境）
2025-08-13 17:19:25,750 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 17:19:25,750 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 17:19:25,750 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 17:19:25,763 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:61] - ✅ 配置生产环境安全策略：启用JWT认证
2025-08-13 17:19:25,764 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:82] - ✅ 生产环境安全配置已生效
2025-08-13 17:19:25,776 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@39d44217, org.springframework.security.web.context.SecurityContextPersistenceFilter@1e3c4c12, org.springframework.security.web.header.HeaderWriterFilter@951053f, org.springframework.security.web.authentication.logout.LogoutFilter@146b4c6c, cathayfuture.opm.adapter.security.JwtAuthenticationFilter@4ba1c1a2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@e74300b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@65a7bc80, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3280a79a, org.springframework.security.web.session.SessionManagementFilter@27ea7c8f, org.springframework.security.web.access.ExceptionTranslationFilter@37bac0f4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3b32f0b4]
2025-08-13 17:19:25,851 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 17:19:26,073 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 17:19:26,128 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 17:19:26,134 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 17:19:26,135 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 2.727 seconds (JVM running for 2.892)
2025-08-13 17:19:45,657 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 17:19:45,657 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 17:19:45,662 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 5 ms
2025-08-13 17:22:20,696 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-13 17:22:20,697 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-13 17:22:20,697 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 6
2025-08-13 17:22:20,697 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: 123456
2025-08-13 17:22:20,813 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-13 17:22:20,894 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-13 17:22:20,914 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:71] - === 检查用户状态 ===
2025-08-13 17:22:20,915 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:73] - 用户是否启用: true
2025-08-13 17:22:20,915 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:81] - 账户是否锁定: false
2025-08-13 17:22:20,915 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:82] - 账户锁定时间: null
2025-08-13 17:22:20,915 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:89] - 密码是否过期: false
2025-08-13 17:22:20,915 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:90] - 密码更新时间: 2025-08-12T14:03:06
2025-08-13 17:22:20,915 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:96] - === 加载用户权限 ===
2025-08-13 17:22:20,920 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:101] - 用户权限列表: [ROLE_ADMIN]
2025-08-13 17:22:20,920 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:102] - 权限数量: 1
2025-08-13 17:22:20,920 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:105] - === 构建UserDetails对象 ===
2025-08-13 17:22:20,920 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:106] - 用户名: admin
2025-08-13 17:22:20,920 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:107] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 17:22:20,920 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:108] - 账户过期: false
2025-08-13 17:22:20,920 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:109] - 账户锁定: false
2025-08-13 17:22:20,920 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:110] - 凭据过期: false
2025-08-13 17:22:20,920 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:111] - 账户禁用: false
2025-08-13 17:22:20,921 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:123] - ✅ 用户 ad***n 认证信息加载完成
2025-08-13 17:22:20,922 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-13 17:22:20,922 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-13 17:22:20,922 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: 123456
2025-08-13 17:22:20,922 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 17:22:20,922 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-13 17:22:20,922 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$FxZ
2025-08-13 17:22:20,922 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-13 17:22:20,922 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-13 17:22:21,002 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: false
2025-08-13 17:22:21,002 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:95] - 尝试手动BCrypt验证...
2025-08-13 17:22:21,002 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:98] - 检测到BCrypt格式密码
2025-08-13 17:22:21,003 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-13 17:22:21,003 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-13 17:22:21,003 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 6
2025-08-13 17:22:21,003 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: 123456
2025-08-13 17:22:21,008 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:71] - === 检查用户状态 ===
2025-08-13 17:22:21,008 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:73] - 用户是否启用: true
2025-08-13 17:22:21,008 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:81] - 账户是否锁定: false
2025-08-13 17:22:21,008 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:82] - 账户锁定时间: null
2025-08-13 17:22:21,008 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:89] - 密码是否过期: false
2025-08-13 17:22:21,008 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:90] - 密码更新时间: 2025-08-12T14:03:06
2025-08-13 17:22:21,009 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:96] - === 加载用户权限 ===
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:101] - 用户权限列表: [ROLE_ADMIN]
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:102] - 权限数量: 1
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:105] - === 构建UserDetails对象 ===
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:106] - 用户名: admin
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:107] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:108] - 账户过期: false
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:109] - 账户锁定: false
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:110] - 凭据过期: false
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:111] - 账户禁用: false
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.DatabaseUserDetailsService [L:123] - ✅ 用户 ad***n 认证信息加载完成
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: 123456
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-13 17:22:21,012 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$FxZ
2025-08-13 17:22:21,013 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-13 17:22:21,013 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-13 17:22:21,091 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: false
2025-08-13 17:22:21,091 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:95] - 尝试手动BCrypt验证...
2025-08-13 17:22:21,091 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.s.LoggingAuthenticationProvider [L:98] - 检测到BCrypt格式密码
2025-08-13 17:23:26,748 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-13 17:23:26,749 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-13 17:23:26,749 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-13 17:23:26,749 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-13 17:23:26,755 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:71] - === 检查用户状态 ===
2025-08-13 17:23:26,755 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:73] - 用户是否启用: true
2025-08-13 17:23:26,755 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:81] - 账户是否锁定: false
2025-08-13 17:23:26,755 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:82] - 账户锁定时间: null
2025-08-13 17:23:26,755 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:89] - 密码是否过期: false
2025-08-13 17:23:26,756 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:90] - 密码更新时间: 2025-08-12T14:03:06
2025-08-13 17:23:26,756 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:96] - === 加载用户权限 ===
2025-08-13 17:23:26,760 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:101] - 用户权限列表: [ROLE_ADMIN]
2025-08-13 17:23:26,760 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:102] - 权限数量: 1
2025-08-13 17:23:26,760 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:105] - === 构建UserDetails对象 ===
2025-08-13 17:23:26,760 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:106] - 用户名: admin
2025-08-13 17:23:26,760 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:107] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:108] - 账户过期: false
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:109] - 账户锁定: false
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:110] - 凭据过期: false
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:111] - 账户禁用: false
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.DatabaseUserDetailsService [L:123] - ✅ 用户 ad***n 认证信息加载完成
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$FxZ
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-13 17:23:26,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-13 17:23:26,841 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: true
2025-08-13 17:23:26,841 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:109] - ✅ 密码验证成功
2025-08-13 17:23:26,841 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:45] - ✅ 认证成功: admin
2025-08-13 17:23:26,841 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-2] c.o.a.s.LoggingAuthenticationProvider [L:46] - 认证结果权限: [ROLE_ADMIN]
2025-08-13 17:23:58,202 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] o.s.api.AbstractOpenApiResource [L:347] - Init duration for springdoc-openapi is: 604 ms
2025-08-13 17:25:47,912 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 17:25:47,930 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-13 17:25:47,934 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-13 17:26:29,898 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 2499 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来/cathayfuture-opm/cathayfuture-opm-adapter)
2025-08-13 17:26:29,899 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: uat
2025-08-13 17:26:30,403 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-13 17:26:30,404 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-13 17:26:30,434 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 26ms. Found 0 repository interfaces.
2025-08-13 17:26:30,610 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f5b0daad] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-13 17:26:30,721 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-13 17:26:30,727 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-13 17:26:30,732 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-13 17:26:30,733 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-13 17:26:30,784 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-13 17:26:30,785 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 874 ms
2025-08-13 17:26:31,344 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-13 17:26:31,429 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:26:31,429 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for *********/*********:63791
2025-08-13 17:26:31,850 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:52] - === 生产环境安全配置 ===
2025-08-13 17:26:31,851 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:53] - 当前环境: [uat]
2025-08-13 17:26:31,851 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:54] - 数据库认证: 启用
2025-08-13 17:26:31,851 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:55] - JWT认证: 启用 ✅
2025-08-13 17:26:31,851 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:56] - =======================
2025-08-13 17:26:31,901 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 34 ms to scan 5 urls, producing 130 keys and 468 values 
2025-08-13 17:26:31,941 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:99] - BMS JWT工具类初始化完成
2025-08-13 17:26:31,941 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:100] - Access Token有效期: 24 小时
2025-08-13 17:26:31,941 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:101] - Refresh Token有效期: 48 小时
2025-08-13 17:26:31,942 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:102] - 发行人: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 17:26:31,942 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:103] - 受众: CATHAY-FUTURE-BMS-SYSTEM
2025-08-13 17:26:32,039 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:90] - 配置数据库认证提供者（生产环境）
2025-08-13 17:26:32,043 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:26] - LoggingAuthenticationProvider 初始化完成
2025-08-13 17:26:32,043 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:27] - UserDetailsService: DatabaseUserDetailsService
2025-08-13 17:26:32,044 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.LoggingAuthenticationProvider [L:28] - PasswordEncoder: BCryptPasswordEncoder
2025-08-13 17:26:32,056 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:61] - ✅ 配置生产环境安全策略：启用JWT认证
2025-08-13 17:26:32,058 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.adapter.security.SecurityConfig [L:82] - ✅ 生产环境安全配置已生效
2025-08-13 17:26:32,069 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3280a79a, org.springframework.security.web.context.SecurityContextPersistenceFilter@7a8e35d1, org.springframework.security.web.header.HeaderWriterFilter@e196238, org.springframework.security.web.authentication.logout.LogoutFilter@5b166420, cathayfuture.opm.adapter.security.JwtAuthenticationFilter@6d7556a8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1aade3c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5b132063, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@79fbcef4, org.springframework.security.web.session.SessionManagementFilter@17e31657, org.springframework.security.web.access.ExceptionTranslationFilter@20c55658, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@77d7d2d0]
2025-08-13 17:26:32,143 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-13 17:26:32,363 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-13 17:26:32,420 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-13 17:26:32,426 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-13 17:26:32,427 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 2.755 seconds (JVM running for 2.923)
2025-08-13 17:26:44,941 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-13 17:26:44,941 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-13 17:26:44,946 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 4 ms
2025-08-13 17:26:55,281 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-3] o.s.api.AbstractOpenApiResource [L:347] - Init duration for springdoc-openapi is: 586 ms
2025-08-13 17:27:29,084 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:33] - === 开始认证流程 ===
2025-08-13 17:27:29,084 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:38] - 认证请求 - 用户名: admin
2025-08-13 17:27:29,084 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:39] - 认证请求 - 密码长度: 8
2025-08-13 17:27:29,084 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:40] - 认证请求 - 密码内容: admin123
2025-08-13 17:27:29,192 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-13 17:27:29,263 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-13 17:27:29,280 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:71] - === 检查用户状态 ===
2025-08-13 17:27:29,280 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:73] - 用户是否启用: true
2025-08-13 17:27:29,280 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:81] - 账户是否锁定: false
2025-08-13 17:27:29,280 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:82] - 账户锁定时间: null
2025-08-13 17:27:29,280 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:89] - 密码是否过期: false
2025-08-13 17:27:29,280 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:90] - 密码更新时间: 2025-08-12T14:03:06
2025-08-13 17:27:29,281 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:96] - === 加载用户权限 ===
2025-08-13 17:27:29,285 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:101] - 用户权限列表: [ROLE_ADMIN]
2025-08-13 17:27:29,285 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:102] - 权限数量: 1
2025-08-13 17:27:29,285 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:105] - === 构建UserDetails对象 ===
2025-08-13 17:27:29,285 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:106] - 用户名: admin
2025-08-13 17:27:29,285 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:107] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 17:27:29,285 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:108] - 账户过期: false
2025-08-13 17:27:29,285 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:109] - 账户锁定: false
2025-08-13 17:27:29,286 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:110] - 凭据过期: false
2025-08-13 17:27:29,286 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:111] - 账户禁用: false
2025-08-13 17:27:29,286 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.DatabaseUserDetailsService [L:123] - ✅ 用户 ad***n 认证信息加载完成
2025-08-13 17:27:29,286 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:58] - === 开始密码验证 ===
2025-08-13 17:27:29,287 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:64] - 用户名: admin
2025-08-13 17:27:29,287 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:65] - 输入密码: admin123
2025-08-13 17:27:29,287 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:66] - 存储密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-13 17:27:29,287 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:67] - 存储密码哈希长度: 60
2025-08-13 17:27:29,287 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:68] - 存储密码哈希格式: $2a$10$FxZ
2025-08-13 17:27:29,287 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:71] - 使用的密码编码器: BCryptPasswordEncoder
2025-08-13 17:27:29,287 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:84] - 开始执行密码匹配...
2025-08-13 17:27:29,365 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:86] - 密码匹配结果: true
2025-08-13 17:27:29,365 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:109] - ✅ 密码验证成功
2025-08-13 17:27:29,365 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:45] - ✅ 认证成功: admin
2025-08-13 17:27:29,365 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-5] c.o.a.s.LoggingAuthenticationProvider [L:46] - 认证结果权限: [ROLE_ADMIN]
2025-08-13 17:31:10,666 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-13 17:31:10,687 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-13 17:31:10,692 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-2] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
