package cathayfuture.opm.adapter.security;

import cathayfuture.opm.infra.common.SpringContextHolder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 认证用户信息服务
 * 从JWT token或Spring Security上下文中获取当前用户的完整信息
 *
 * <AUTHOR> Code
 * @date 2025-08-14
 */
@Slf4j
@Service
public class AuthenticatedUserService {

    /**
     * 获取当前认证用户的用户名
     */
    public String getCurrentUsername() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            return auth.getName();
        }
        return null;
    }

    /**
     * 获取当前认证用户的用户ID
     */
    public Long getCurrentUserId() {
        String userInfoJson = getCurrentUserInfoJson();
        if (StringUtils.hasText(userInfoJson)) {
            try {
                JSONObject userInfo = JSON.parseObject(userInfoJson);
                if (userInfo != null && userInfo.containsKey("userId")) {
                    Object userIdObj = userInfo.get("userId");
                    if (userIdObj instanceof Number) {
                        return ((Number) userIdObj).longValue();
                    }
                    if (userIdObj instanceof String) {
                        return Long.parseLong((String) userIdObj);
                    }
                }
            } catch (Exception e) {
                log.debug("解析用户ID失败: {}", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 获取当前认证用户的真实姓名
     */
    public String getCurrentUserRealName() {
        String userInfoJson = getCurrentUserInfoJson();
        if (StringUtils.hasText(userInfoJson)) {
            try {
                JSONObject userInfo = JSON.parseObject(userInfoJson);
                if (userInfo != null) {
                    // 优先使用真实姓名
                    String realName = userInfo.getString("realName");
                    if (StringUtils.hasText(realName)) {
                        return realName;
                    }
                    // 其次使用用户名
                    String userName = userInfo.getString("userName");
                    if (StringUtils.hasText(userName)) {
                        return userName;
                    }
                    // 最后使用OA姓名
                    String oaName = userInfo.getString("oaName");
                    if (StringUtils.hasText(oaName)) {
                        return oaName;
                    }
                }
            } catch (Exception e) {
                log.debug("解析用户真实姓名失败: {}", e.getMessage());
            }
        }
        // 如果都没有，返回用户名
        return getCurrentUsername();
    }

    /**
     * 获取当前认证用户的角色ID列表
     */
    public String getCurrentUserRoleIds() {
        String userInfoJson = getCurrentUserInfoJson();
        if (StringUtils.hasText(userInfoJson)) {
            try {
                JSONObject userInfo = JSON.parseObject(userInfoJson);
                if (userInfo != null && userInfo.containsKey("roleIds")) {
                    String roleIds = userInfo.getString("roleIds");
                    if (StringUtils.hasText(roleIds)) {
                        return roleIds;
                    }
                }
            } catch (Exception e) {
                log.debug("解析用户角色ID失败: {}", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 获取当前认证用户的岗位代码
     */
    public String getCurrentUserPostCode() {
        String userInfoJson = getCurrentUserInfoJson();
        if (StringUtils.hasText(userInfoJson)) {
            try {
                JSONObject userInfo = JSON.parseObject(userInfoJson);
                if (userInfo != null && userInfo.containsKey("postCode")) {
                    String postCode = userInfo.getString("postCode");
                    if (StringUtils.hasText(postCode)) {
                        return postCode;
                    }
                }
            } catch (Exception e) {
                log.debug("解析用户岗位代码失败: {}", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 获取当前认证用户的岗位名称
     */
    public String getCurrentUserPostName() {
        String userInfoJson = getCurrentUserInfoJson();
        if (StringUtils.hasText(userInfoJson)) {
            try {
                JSONObject userInfo = JSON.parseObject(userInfoJson);
                if (userInfo != null && userInfo.containsKey("postName")) {
                    String postName = userInfo.getString("postName");
                    if (StringUtils.hasText(postName)) {
                        return postName;
                    }
                }
            } catch (Exception e) {
                log.debug("解析用户岗位名称失败: {}", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 检查当前是否有认证用户
     */
    public boolean hasAuthenticatedUser() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal());
    }

    /**
     * 获取当前用户的完整信息JSON字符串
     * 从JWT token中提取
     */
    private String getCurrentUserInfoJson() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth instanceof JwtAuthenticationToken) {
            JwtAuthenticationToken jwtAuth = (JwtAuthenticationToken) auth;
            String token = (String) jwtAuth.getCredentials();
            if (StringUtils.hasText(token)) {
                try {
                    return BmsJwtUtils.getUserInfoFromBmsAccessToken(token);
                } catch (Exception e) {
                    log.debug("从JWT token获取用户信息失败: {}", e.getMessage());
                }
            }
        }
        return null;
    }

    /**
     * 获取当前用户的所有信息（用于调试）
     */
    public String getCurrentUserInfo() {
        if (!hasAuthenticatedUser()) {
            return "未认证用户";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("用户名: ").append(getCurrentUsername()).append("\n");
        sb.append("用户ID: ").append(getCurrentUserId()).append("\n");
        sb.append("真实姓名: ").append(getCurrentUserRealName()).append("\n");
        sb.append("角色ID: ").append(getCurrentUserRoleIds()).append("\n");
        sb.append("岗位代码: ").append(getCurrentUserPostCode()).append("\n");
        sb.append("岗位名称: ").append(getCurrentUserPostName());

        return sb.toString();
    }

    // ========== 静态方法供其他模块使用 ==========
    
    /**
     * 静态方法 - 获取当前认证用户的用户名
     */
    public static String getAuthenticatedUsername() {
        try {
            AuthenticatedUserService service = SpringContextHolder.getBean(AuthenticatedUserService.class);
            return service.getCurrentUsername();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 静态方法 - 获取当前认证用户的用户ID
     */
    public static Long getAuthenticatedUserId() {
        try {
            AuthenticatedUserService service = SpringContextHolder.getBean(AuthenticatedUserService.class);
            return service.getCurrentUserId();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 静态方法 - 获取当前认证用户的真实姓名
     */
    public static String getAuthenticatedUserRealName() {
        try {
            AuthenticatedUserService service = SpringContextHolder.getBean(AuthenticatedUserService.class);
            return service.getCurrentUserRealName();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 静态方法 - 获取当前认证用户的角色ID列表
     */
    public static String getAuthenticatedUserRoleIds() {
        try {
            AuthenticatedUserService service = SpringContextHolder.getBean(AuthenticatedUserService.class);
            return service.getCurrentUserRoleIds();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 静态方法 - 获取当前认证用户的岗位代码
     */
    public static String getAuthenticatedUserPostCode() {
        try {
            AuthenticatedUserService service = SpringContextHolder.getBean(AuthenticatedUserService.class);
            return service.getCurrentUserPostCode();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 静态方法 - 获取当前认证用户的岗位名称
     */
    public static String getAuthenticatedUserPostName() {
        try {
            AuthenticatedUserService service = SpringContextHolder.getBean(AuthenticatedUserService.class);
            return service.getCurrentUserPostName();
        } catch (Exception e) {
            return null;
        }
    }
}