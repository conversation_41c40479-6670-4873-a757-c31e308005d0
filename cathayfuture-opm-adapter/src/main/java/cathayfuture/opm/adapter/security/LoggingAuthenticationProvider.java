package cathayfuture.opm.adapter.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 带日志的认证提供者
 * 用于调试认证流程（已移除敏感信息记录）
 * 
 * 注意：此类仅用于开发和测试环境的问题诊断
 * 生产环境建议使用标准的DaoAuthenticationProvider
 */
@Slf4j
public class LoggingAuthenticationProvider extends DaoAuthenticationProvider {

    public LoggingAuthenticationProvider(UserDetailsService userDetailsService, PasswordEncoder passwordEncoder) {
        super();
        setUserDetailsService(userDetailsService);
        setPasswordEncoder(passwordEncoder);
        log.info("LoggingAuthenticationProvider 初始化完成");
        log.info("UserDetailsService: {}", userDetailsService.getClass().getSimpleName());
        log.info("PasswordEncoder: {}", passwordEncoder.getClass().getSimpleName());
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        // 添加递归调用保护
        if (authentication == null) {
            log.error("认证对象为空");
            throw new BadCredentialsException("认证对象不能为空");
        }

        // 检查是否是我们支持的认证类型
        if (!supports(authentication.getClass())) {
            log.debug("不支持的认证类型: {}", authentication.getClass().getSimpleName());
            return null;
        }

        log.info("=== 开始认证流程 ===");

        String username = authentication.getName();
        Object credentials = authentication.getCredentials();

        if (username == null || credentials == null) {
            log.error("用户名或密码为空");
            throw new BadCredentialsException("用户名和密码不能为空");
        }

        String password = credentials.toString();
        log.info("认证请求 - 用户名: {}", username);
        log.info("认证请求 - 密码长度: {}", password.length());

        try {
            // 直接调用父类的认证逻辑，避免可能的循环调用
            UserDetails userDetails = getUserDetailsService().loadUserByUsername(username);

            // 执行密码检查
            additionalAuthenticationChecks(userDetails,
                    new UsernamePasswordAuthenticationToken(authentication.getPrincipal(), authentication.getCredentials()));

            // 创建成功的认证对象
            UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(
                    userDetails, authentication.getCredentials(), userDetails.getAuthorities());
            result.setDetails(authentication.getDetails());

            log.info("✅ 认证成功: {}", username);
            log.info("认证结果权限: {}", result.getAuthorities());
            return result;

        } catch (UsernameNotFoundException e) {
            log.error("❌ 用户不存在: {}", username);
            throw new BadCredentialsException("用户名或密码错误");
        } catch (AuthenticationException e) {
            log.error("❌ 认证失败: {} - {}", username, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("❌ 认证过程中发生异常: {} - {}", username, e.getMessage());
            throw new AuthenticationServiceException("认证服务异常", e);
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }

    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails,
                                                  UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {

        log.info("=== 开始密码验证 ===");

        String username = userDetails.getUsername();
        String presentedPassword = authentication.getCredentials().toString();
        String storedPassword = userDetails.getPassword();

        log.info("用户名: {}", username);
        log.info("输入密码长度: {}", presentedPassword != null ? presentedPassword.length() : 0);
        log.info("存储密码哈希长度: {}", storedPassword != null ? storedPassword.length() : 0);
        log.info("存储密码哈希前缀: {}", storedPassword != null && storedPassword.length() > 6 ? storedPassword.substring(0, 6) + "..." : "null");

        PasswordEncoder encoder = getPasswordEncoder();
        log.info("使用的密码编码器: {}", encoder.getClass().getSimpleName());

        if (presentedPassword == null) {
            log.error("❌ 输入密码为空");
            throw new BadCredentialsException("密码不能为空");
        }

        if (storedPassword == null || storedPassword.isEmpty()) {
            log.error("❌ 存储密码为空");
            throw new BadCredentialsException("用户密码未设置");
        }

        // 执行密码匹配
        log.info("开始执行密码匹配...");
        boolean matches = encoder.matches(presentedPassword, storedPassword);
        log.info("密码匹配结果: {}", matches);

        if (!matches) {
            log.error("❌ 密码不匹配 - 用户: {}", username);

            // 尝试手动验证（用于调试）
            try {
                log.info("尝试手动BCrypt验证...");
                if (storedPassword.startsWith("$2a$") || storedPassword.startsWith("$2b$") || storedPassword.startsWith("$2y$")) {
                    log.info("检测到BCrypt格式密码");
                } else {
                    log.warn("密码不是BCrypt格式");
                }
            } catch (Exception e) {
                log.error("手动验证异常: {}", e.getMessage());
            }

            throw new BadCredentialsException("用户名或密码错误");
        }

        log.info("✅ 密码验证成功");
    }
}

