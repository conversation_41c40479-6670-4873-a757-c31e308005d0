package cathayfuture.opm.adapter.security;

import cathayfuture.opm.adapter.common.SecurityLogUtils;
import cathayfuture.opm.domain.system.SysUserEntity;
import cathayfuture.opm.domain.system.SysRoleEntity;
import cathayfuture.opm.domain.system.repository.SysUserRepository;
import cathayfuture.opm.domain.system.repository.SysRoleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基于数据库的用户详情服务
 * 替换原有的FixedUserDetailsService，从数据库加载用户信息
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Service
public class DatabaseUserDetailsService implements UserDetailsService {

    private final SysUserRepository userRepository;
    private final SysRoleRepository roleRepository;

    public DatabaseUserDetailsService(SysUserRepository userRepository, 
                                    SysRoleRepository roleRepository) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("=== 开始数据库用户认证流程 ===");
        log.debug("请求认证的用户名: {}", SecurityLogUtils.maskUsername(username));

        // 1. 从数据库查询用户信息
        SysUserEntity userEntity = userRepository.findByUsername(username);
        if (userEntity == null) {
            log.warn("❌ 用户不存在: {}", SecurityLogUtils.maskUsername(username));
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        log.debug("✅ 找到用户: {} (ID: {})", SecurityLogUtils.maskUsername(username), SecurityLogUtils.maskUserId(userEntity.getId()));
        log.debug("用户编码: {}", userEntity.getUserCode());
        log.debug("真实姓名: {}", SecurityLogUtils.maskRealName(userEntity.getRealName()));
        log.debug("OA姓名: {}", SecurityLogUtils.maskRealName(userEntity.getOaName()));
        log.debug("用户状态: {}", userEntity.getStatus());
        
        // 安全记录密码信息（仅在DEBUG级别且非生产环境）
        if (log.isDebugEnabled()) {
            log.debug("密码哈希长度: {}", userEntity.getPassword() != null ? userEntity.getPassword().length() : 0);
            log.debug("密码哈希格式: {}", userEntity.getPassword() != null ? userEntity.getPassword().substring(0, Math.min(10, userEntity.getPassword().length())) : "null");
        } else {
            log.info("密码信息: {}", userEntity.getPassword() != null ? "已加密" : "未设置");
        }

        // 2. 检查用户状态
        log.info("=== 检查用户状态 ===");
        boolean isEnabled = userEntity.isEnabled();
        log.info("用户是否启用: {}", isEnabled);
        if (!isEnabled) {
            log.warn("❌ 用户已被禁用: {}", username);
            throw new UsernameNotFoundException("用户已被禁用: " + username);
        }

        // 3. 检查账户是否被锁定
        boolean accountLocked = userEntity.isAccountLocked();
        log.info("账户是否锁定: {}", accountLocked);
        log.info("账户锁定时间: {}", userEntity.getAccountLockedTime());
        if (accountLocked) {
            log.warn("⚠️ 用户账户已被锁定: {}", username);
        }

        // 4. 检查密码是否过期
        boolean credentialsExpired = userEntity.isPasswordExpired();
        log.info("密码是否过期: {}", credentialsExpired);
        log.info("密码更新时间: {}", userEntity.getPasswordUpdateTime());
        if (credentialsExpired) {
            log.warn("⚠️ 用户密码已过期: {}", username);
        }

        // 5. 获取用户角色和权限
        log.info("=== 加载用户权限 ===");
        List<GrantedAuthority> authorities = getUserAuthorities(userEntity.getId());
        List<String> authorityNames = authorities.stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        log.info("用户权限列表: {}", authorityNames);
        log.info("权限数量: {}", authorities.size());

        // 6. 构建UserDetails对象
        log.info("=== 构建UserDetails对象 ===");
        log.info("用户名: {}", userEntity.getUsername());
        log.info("密码哈希: {}", userEntity.getPassword());
        log.info("账户过期: false");
        log.info("账户锁定: {}", accountLocked);
        log.info("凭据过期: {}", credentialsExpired);
        log.info("账户禁用: {}", !userEntity.isEnabled());

        UserDetails userDetails = User.builder()
                .username(userEntity.getUsername())
                .password(userEntity.getPassword()) // 数据库中已经是BCrypt加密的密码
                .authorities(authorities)
                .accountExpired(false) // 暂不实现账户过期逻辑
                .accountLocked(accountLocked)
                .credentialsExpired(credentialsExpired)
                .disabled(!userEntity.isEnabled())
                .build();

        log.info("✅ 用户 {} 认证信息加载完成", SecurityLogUtils.maskUsername(username));
        log.debug("=== DatabaseUserDetailsService处理完成 ===");

        return userDetails;
    }

    /**
     * 获取用户的权限列表
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    private List<GrantedAuthority> getUserAuthorities(Integer userId) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        
        try {
            // 获取用户的角色列表
            List<SysRoleEntity> roles = roleRepository.findByUserId(userId);
            
            if (!CollectionUtils.isEmpty(roles)) {
                // 添加角色权限（以ROLE_开头）
                for (SysRoleEntity role : roles) {
                    if (role.isEnabled()) {
                        authorities.add(new SimpleGrantedAuthority(role.getRoleCode()));
                        log.debug("为用户 {} 添加角色权限: {}", userId, role.getRoleCode());
                    }
                }
                
                // TODO: 如果需要更细粒度的权限控制，可以在这里添加具体的权限
                // List<SysPermissionEntity> permissions = permissionRepository.findByUserId(userId);
                // for (SysPermissionEntity permission : permissions) {
                //     if (permission.isEnabled()) {
                //         authorities.add(new SimpleGrantedAuthority(permission.getPermissionCode()));
                //     }
                // }
            }
            
            // 如果用户没有任何角色，给一个默认角色
            if (authorities.isEmpty()) {
                authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
                log.debug("用户 {} 没有分配角色，使用默认角色 ROLE_USER", userId);
            }
            
        } catch (Exception e) {
            log.error("获取用户 {} 权限时发生错误", userId, e);
            // 发生错误时给一个默认角色
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        }
        
        return authorities;
    }

    /**
     * 更新用户最后登录信息
     * 
     * @param username 用户名
     * @param loginIp 登录IP
     */
    public void updateLastLoginInfo(String username, String loginIp) {
        try {
            SysUserEntity userEntity = userRepository.findByUsername(username);
            if (userEntity != null) {
                userEntity.setLastLoginTime(LocalDateTime.now());
                userEntity.setLastLoginIp(loginIp);
                userEntity.setLoginFailureCount(0); // 登录成功后重置失败次数
                userRepository.updateById(userEntity);
                log.debug("更新用户 {} 最后登录信息成功", username);
            }
        } catch (Exception e) {
            log.error("更新用户 {} 最后登录信息失败", username, e);
        }
    }

    /**
     * 增加登录失败次数
     * 
     * @param username 用户名
     */
    public void incrementLoginFailureCount(String username) {
        try {
            SysUserEntity userEntity = userRepository.findByUsername(username);
            if (userEntity != null) {
                int failureCount = userEntity.getLoginFailureCount() == null ? 0 : userEntity.getLoginFailureCount();
                userEntity.setLoginFailureCount(failureCount + 1);
                
                // 如果失败次数达到5次，锁定账户1小时
                if (userEntity.getLoginFailureCount() >= 5) {
                    userEntity.setAccountLockedTime(LocalDateTime.now().plusHours(1));
                    log.warn("用户 {} 登录失败次数达到5次，账户已被锁定1小时", username);
                }
                
                userRepository.updateById(userEntity);
                log.debug("用户 {} 登录失败次数增加到 {}", username, userEntity.getLoginFailureCount());
            }
        } catch (Exception e) {
            log.error("增加用户 {} 登录失败次数失败", username, e);
        }
    }

    /**
     * 根据用户名获取用户ID
     */
    public Long getUserIdByUsername(String username) {
        try {
            SysUserEntity userEntity = userRepository.findByUsername(username);
            if (userEntity != null) {
                return userEntity.getId() != null ? userEntity.getId().longValue() : null;
            }
            log.warn("获取用户ID失败，用户不存在: {}", username);
            return null;
        } catch (Exception e) {
            log.error("根据用户名获取用户ID失败: {}", username, e);
            return null;
        }
    }

    /**
     * 根据用户名获取完整的用户信息（用于JWT token）
     */
    public Map<String, Object> getUserInfoByUsername(String username) {
        try {
            SysUserEntity userEntity = userRepository.findByUsername(username);
            if (userEntity != null) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("userId", userEntity.getId() != null ? userEntity.getId().longValue() : null);
                userInfo.put("username", userEntity.getUsername());
                userInfo.put("userCode", userEntity.getUserCode());
                userInfo.put("realName", userEntity.getRealName());
                userInfo.put("oaName", userEntity.getOaName());
                
                // 获取用户角色信息
                List<SysRoleEntity> roles = roleRepository.findByUserId(userEntity.getId());
                if (roles != null && !roles.isEmpty()) {
                    String roleIds = roles.stream()
                        .filter(SysRoleEntity::isEnabled)
                        .map(role -> String.valueOf(role.getId()))
                        .collect(java.util.stream.Collectors.joining(","));
                    userInfo.put("roleIds", roleIds);
                    
                    // 获取第一个启用的角色作为主要角色
                    SysRoleEntity primaryRole = roles.stream()
                        .filter(SysRoleEntity::isEnabled)
                        .findFirst()
                        .orElse(null);
                    if (primaryRole != null) {
                        userInfo.put("roleName", primaryRole.getRoleName());
                        userInfo.put("roleCode", primaryRole.getRoleCode());
                    }
                } else {
                    userInfo.put("roleIds", "0");
                }
                
                // TODO: 从其他表获取岗位信息，目前使用默认值
                userInfo.put("postCode", "USER");
                userInfo.put("postName", "用户");
                
                return userInfo;
            }
            log.warn("获取用户信息失败，用户不存在: {}", username);
            return null;
        } catch (Exception e) {
            log.error("根据用户名获取用户信息失败: {}", username, e);
            return null;
        }
    }

}
