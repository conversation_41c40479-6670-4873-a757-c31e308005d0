package cathayfuture.opm.adapter.security;

import cathayfuture.opm.app.common.RedisLockService;
import com.dtyunxi.huieryun.cache.api.IRedisCacheService;
import com.dtyunxi.huieryun.lock.api.Mutex;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Token管理服务
 * 负责JWT token的Redis存储、验证和失效管理
 * 
 * <AUTHOR>
 * @since 2025-08-12
 */
@Slf4j
@Service
public class TokenManagementService {
    
    private static final String TOKEN_CACHE_GROUP = "JWT_TOKEN_CACHE";
    private static final String REFRESH_TOKEN_CACHE_GROUP = "JWT_REFRESH_TOKEN_CACHE";
    private static final String USER_TOKEN_MAPPING_GROUP = "USER_TOKEN_MAPPING";
    
    // Access Token有效期：1天（与JWT中的过期时间保持一致）
    private static final int ACCESS_TOKEN_EXPIRE_HOURS = 1 * 24;
    // Refresh Token有效期：2天
    private static final int REFRESH_TOKEN_EXPIRE_HOURS = 2 * 24;
    
    @Resource
    private IRedisCacheService cacheService;
    
    @Resource
    private RedisLockService redisLockService;
    
    /**
     * 是否启用单用户单Token策略（默认启用）
     * true: 新登录会使旧token失效（推荐）
     * false: 允许同一用户多个有效token（不推荐）
     */
    @Value("${cathay.security.token.single-token-per-user:true}")
    private boolean singleTokenPerUser;
    
    /**
     * 存储token到Redis
     * @param token JWT token
     * @param username 用户名
     * @param userInfo 用户信息JSON字符串
     * @return 是否存储成功
     */
    public boolean storeToken(String token, String username, String userInfo) {
        // 使用RedisLockService获取分布式锁，彻底解决竞态条件
        String lockKey = username; // 使用用户名作为锁的primaryKey
        Mutex mutex = null;
        
        try {
            // 获取分布式锁，等待最多10秒，锁超时30秒
            mutex = redisLockService.lock("USER_LOGIN_LOCK", lockKey);
            if (mutex == null) {
                log.warn("用户 {} 正在登录中，请稍后重试", username);
                return false;
            }
            
            log.debug("获取登录锁成功: username={}", username);
            
            // 在锁保护下执行业务逻辑
            // 1. 如果启用单用户单token策略，清理用户的旧token
            if (singleTokenPerUser) {
                invalidateUserTokensSilently(username);
                log.debug("单用户单Token策略：已清理用户 {} 的旧token", username);
            }

            // 2. 存储新的token信息
            TokenInfo tokenInfo = new TokenInfo();
            tokenInfo.setToken(token);
            tokenInfo.setUsername(username);
            tokenInfo.setUserInfo(userInfo);
            tokenInfo.setCreateTime(System.currentTimeMillis());
            tokenInfo.setLastAccessTime(System.currentTimeMillis());

            // 3. 将token存储到Redis，设置过期时间（Redis自动过期机制）
            cacheService.setCache(TOKEN_CACHE_GROUP, token, tokenInfo, ACCESS_TOKEN_EXPIRE_HOURS * 3600);

            // 4. 建立用户名到token的映射（用于登出和单用户限制）
            cacheService.setCache(USER_TOKEN_MAPPING_GROUP, username, token, ACCESS_TOKEN_EXPIRE_HOURS * 3600);

            log.debug("Token存储成功: username={}, token前缀={}, 单token策略={}",
                username, token.substring(0, Math.min(20, token.length())), singleTokenPerUser);
            return true;
            
        } catch (Exception e) {
            log.error("Token存储失败: username={}, error={}", username, e.getMessage(), e);
            return false;
        } finally {
            // 确保锁被释放
            if (mutex != null) {
                try {
                    boolean unlocked = redisLockService.unlock(mutex);
                    log.debug("释放登录锁{}: username={}", unlocked ? "成功" : "失败", username);
                } catch (Exception unlockEx) {
                    log.error("释放登录锁异常: username={}, error={}", username, unlockEx.getMessage());
                }
            }
        }
    }

    
    /**
     * 验证token是否有效
     * 使用双阶段验证和分布式锁防止令牌验证与失效的竞态条件
     * @param token JWT token
     * @return 是否有效
     */
    public boolean isTokenValid(String token) {
        if (!StringUtils.hasText(token)) {
            return false;
        }
        
        try {
            // 第一阶段：快速检查token是否存在（无锁，过滤大部分无效token）
            TokenInfo tokenInfo = cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
            if (tokenInfo == null) {
                log.debug("Token在Redis中不存在: {}", token.substring(0, Math.min(20, token.length())));
                return false;
            }
            
            // 第二阶段：加锁验证，防止与storeToken()/invalidateToken()的竞态条件
            String username = tokenInfo.getUsername();
            Mutex mutex = null;
            
            try {
                // 使用与登录/登出相同的分布式锁，确保串行化
                mutex = redisLockService.lock("USER_LOGIN_LOCK", username);
                if (mutex == null) {
                    // 保守策略：无法获取锁时返回false，确保安全性
                    log.warn("无法获取用户锁进行token验证，返回false: username={}", username);
                    return false;
                }
                
                log.debug("获取登录锁成功，开始token验证: username={}", username);
                
                // 在锁保护下重新验证token（可能在等待锁期间被其他线程删除）
                TokenInfo revalidatedInfo = cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
                if (revalidatedInfo == null) {
                    log.debug("Token在加锁后验证时已不存在: username={}", username);
                    return false;
                }
                
                // 优化访问时间更新：只有超过5分钟才更新，减少并发冲突和Redis压力
                long currentTime = System.currentTimeMillis();
                long timeSinceLastAccess = currentTime - revalidatedInfo.getLastAccessTime();
                
                if (timeSinceLastAccess > 5 * 60 * 1000) { // 5分钟 = 5 * 60 * 1000毫秒
                    try {
                        revalidatedInfo.setLastAccessTime(currentTime);
                        cacheService.setCache(TOKEN_CACHE_GROUP, token, revalidatedInfo, ACCESS_TOKEN_EXPIRE_HOURS * 3600);
                        log.trace("更新Token访问时间: username={}", revalidatedInfo.getUsername());
                    } catch (Exception updateEx) {
                        // 访问时间更新失败不影响token验证结果，只记录trace级别日志
                        log.trace("更新Token访问时间失败，不影响验证结果: username={}, error={}", 
                            revalidatedInfo.getUsername(), updateEx.getMessage());
                    }
                }
                
                log.debug("Token验证成功: username={}", revalidatedInfo.getUsername());
                return true;
                
            } finally {
                // 确保锁被释放
                if (mutex != null) {
                    try {
                        boolean unlocked = redisLockService.unlock(mutex);
                        log.debug("释放登录锁{}: username={}", unlocked ? "成功" : "失败", username);
                    } catch (Exception unlockEx) {
                        log.error("释放登录锁异常: username={}, error={}", username, unlockEx.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Token验证失败: error={}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取token信息
     * @param token JWT token
     * @return token信息
     */
    public TokenInfo getTokenInfo(String token) {
        if (!StringUtils.hasText(token)) {
            return null;
        }
        
        try {
            return cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
        } catch (Exception e) {
            log.error("获取Token信息失败: error={}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 使token失效（登出）
     * @param token JWT token
     * @return 是否成功
     */
    public boolean invalidateToken(String token) {
        if (!StringUtils.hasText(token)) {
            return false;
        }
        
        // 先获取token信息以确定用户名（在锁外执行，避免无效token也要获取锁）
        TokenInfo tokenInfo;
        try {
            tokenInfo = cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
            if (tokenInfo == null) {
                log.debug("Token已不存在，无需失效: token前缀={}", token.substring(0, Math.min(20, token.length())));
                return true; // token不存在视为已失效，返回成功
            }
        } catch (Exception e) {
            log.error("获取Token信息失败: error={}", e.getMessage(), e);
            return false;
        }
        
        // 使用分布式锁确保与登录操作串行化
        String username = tokenInfo.getUsername();
        String lockKey = username;
        Mutex mutex = null;
        
        try {
            // 获取与登录操作相同的分布式锁
            mutex = redisLockService.lock("USER_LOGIN_LOCK", lockKey);
            if (mutex == null) {
                log.warn("用户 {} 正在进行登录操作，Token失效请稍后重试", username);
                return false;
            }
            
            log.debug("获取登录锁成功，开始Token失效: username={}", username);
            
            // 在锁保护下执行失效操作
            // 1. 再次验证token是否仍然存在（可能在等待锁期间已被其他操作删除）
            TokenInfo currentTokenInfo = cacheService.getCache(TOKEN_CACHE_GROUP, token, TokenInfo.class);
            if (currentTokenInfo != null) {
                // 2. 删除用户名到token的映射
                cacheService.delCache(USER_TOKEN_MAPPING_GROUP, currentTokenInfo.getUsername());
                log.debug("删除用户token映射: username={}", currentTokenInfo.getUsername());
            }
            
            // 3. 删除token
            cacheService.delCache(TOKEN_CACHE_GROUP, token);
            
            log.info("Token失效成功: username={}, token前缀={}", username, token.substring(0, Math.min(20, token.length())));
            return true;
            
        } catch (Exception e) {
            log.error("Token失效失败: username={}, error={}", username, e.getMessage(), e);
            return false;
        } finally {
            // 确保锁被释放
            if (mutex != null) {
                try {
                    boolean unlocked = redisLockService.unlock(mutex);
                    log.debug("释放登录锁{}: username={}", unlocked ? "成功" : "失败", username);
                } catch (Exception unlockEx) {
                    log.error("释放登录锁异常: username={}, error={}", username, unlockEx.getMessage());
                }
            }
        }
    }
    
    /**
     * 使用户的所有token失效（强制登出）
     * @param username 用户名
     * @return 是否成功
     */
    public boolean invalidateUserTokens(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        
        // 使用分布式锁确保与登录/登出操作串行化
        String lockKey = username;
        Mutex mutex = null;
        
        try {
            // 获取与登录操作相同的分布式锁
            mutex = redisLockService.lock("USER_LOGIN_LOCK", lockKey);
            if (mutex == null) {
                log.warn("用户 {} 正在进行登录操作，强制登出请稍后重试", username);
                return false;
            }
            
            log.debug("获取登录锁成功，开始强制用户登出: username={}", username);
            
            // 在锁保护下执行强制登出操作
            // 1. 获取用户当前的token
            String currentToken = cacheService.getCache(USER_TOKEN_MAPPING_GROUP, username, String.class);
            if (StringUtils.hasText(currentToken)) {
                // 2. 删除token
                cacheService.delCache(TOKEN_CACHE_GROUP, currentToken);
                log.debug("删除用户token: username={}, token前缀={}", username, currentToken.substring(0, Math.min(20, currentToken.length())));
            }
            
            // 3. 删除用户名到token的映射
            cacheService.delCache(USER_TOKEN_MAPPING_GROUP, username);
            
            log.info("用户所有Token失效成功: username={}", username);
            return true;
            
        } catch (Exception e) {
            log.error("用户Token失效失败: username={}, error={}", username, e.getMessage(), e);
            return false;
        } finally {
            // 确保锁被释放
            if (mutex != null) {
                try {
                    boolean unlocked = redisLockService.unlock(mutex);
                    log.debug("释放登录锁{}: username={}", unlocked ? "成功" : "失败", username);
                } catch (Exception unlockEx) {
                    log.error("释放登录锁异常: username={}, error={}", username, unlockEx.getMessage());
                }
            }
        }
    }
    
    /**
     * 静默使用户的所有token失效（不记录INFO日志）
     * 用于单用户单token策略的内部清理
     * @param username 用户名
     * @return 是否成功
     */
    private boolean invalidateUserTokensSilently(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        
        try {
            // 1. 获取用户当前的token
            String currentToken = cacheService.getCache(USER_TOKEN_MAPPING_GROUP, username, String.class);
            if (StringUtils.hasText(currentToken)) {
                // 2. 删除token
                cacheService.delCache(TOKEN_CACHE_GROUP, currentToken);
                log.debug("静默删除用户token: username={}, token前缀={}", username, currentToken.substring(0, Math.min(20, currentToken.length())));
            }
            
            // 3. 删除用户名到token的映射
            cacheService.delCache(USER_TOKEN_MAPPING_GROUP, username);
            
            return true;
        } catch (Exception e) {
            log.debug("静默清理用户Token失败: username={}, error={}", username, e.getMessage());
            return false;
        }
    }
    
    /**
     * 生成refresh token（使用JWT格式）
     * 使用分布式锁防止多线程同时为同一用户生成刷新令牌
     * @param username 用户名
     * @return refresh token
     */
    public String generateRefreshToken(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }
        
        // 使用与访问令牌相同的分布式锁机制，确保认证操作串行化
        String lockKey = username;
        Mutex mutex = null;
        
        try {
            // 获取分布式锁，确保与其他认证操作（登录/登出）串行化
            mutex = redisLockService.lock("USER_LOGIN_LOCK", lockKey);
            if (mutex == null) {
                log.warn("用户 {} 正在进行认证操作，刷新令牌生成请稍后重试", username);
                return null;
            }
            
            log.debug("获取登录锁成功，开始生成刷新令牌: username={}", username);
            
            // 在锁保护下生成刷新令牌
            // 创建refresh token的payload
            Map<String, Object> payload = new HashMap<>();
            payload.put("username", username);
            payload.put("type", BmsJwtUtils.BMS_TOKEN_TYPE_REFRESH);
            
            // 使用BmsJwtUtils生成JWT格式的refresh token
            String refreshToken = BmsJwtUtils.createBmsRefreshToken(payload);
            
            if (refreshToken == null) {
                log.error("JWT Refresh Token生成失败: username={}", username);
                return null;
            }
            
            // 原子化存储refresh token信息到Redis，设置2天过期
            RefreshTokenInfo refreshTokenInfo = new RefreshTokenInfo();
            refreshTokenInfo.setRefreshToken(refreshToken);
            refreshTokenInfo.setUsername(username);
            refreshTokenInfo.setCreateTime(System.currentTimeMillis());
            
            cacheService.setCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken, refreshTokenInfo, REFRESH_TOKEN_EXPIRE_HOURS * 3600);
            
            log.debug("JWT Refresh Token生成成功: username={}", username);
            return refreshToken;
            
        } catch (Exception e) {
            log.error("JWT Refresh Token生成失败: username={}, error={}", username, e.getMessage(), e);
            return null;
        } finally {
            // 确保锁被释放
            if (mutex != null) {
                try {
                    boolean unlocked = redisLockService.unlock(mutex);
                    log.debug("释放登录锁{}: username={}", unlocked ? "成功" : "失败", username);
                } catch (Exception unlockEx) {
                    log.error("释放登录锁异常: username={}, error={}", username, unlockEx.getMessage());
                }
            }
        }
    }
    
    /**
     * 验证refresh token（JWT格式）
     * 使用双阶段验证和分布式锁防止刷新令牌验证与失效的竞态条件
     * @param refreshToken refresh token
     * @return 用户名，如果无效返回null
     */
    public String validateRefreshToken(String refreshToken) {
        if (!StringUtils.hasText(refreshToken)) {
            return null;
        }
        
        try {
            // 第一阶段：JWT签名和基本验证（无锁，快速过滤无效token）
            if (!BmsJwtUtils.verifyBmsRefreshToken(refreshToken)) {
                log.debug("BMS Refresh Token JWT验证失败");
                return null;
            }
            
            // 第二阶段：检查token在Redis中是否存在
            RefreshTokenInfo refreshTokenInfo = cacheService.getCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken, RefreshTokenInfo.class);
            if (refreshTokenInfo == null) {
                log.debug("Refresh Token在Redis中不存在或已过期");
                return null;
            }
            
            // 第三阶段：加锁验证，防止与失效操作的竞态条件
            String username = refreshTokenInfo.getUsername();
            Mutex mutex = null;
            
            try {
                // 使用与其他认证操作相同的分布式锁
                mutex = redisLockService.lock("USER_LOGIN_LOCK", username);
                if (mutex == null) {
                    // 保守策略：无法获取锁时返回null
                    log.warn("无法获取用户锁进行刷新令牌验证: username={}", username);
                    return null;
                }
                
                log.debug("获取登录锁成功，开始刷新令牌验证: username={}", username);
                
                // 在锁保护下重新验证token（可能在等待锁期间被其他线程删除）
                RefreshTokenInfo revalidatedInfo = cacheService.getCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken, RefreshTokenInfo.class);
                if (revalidatedInfo == null) {
                    log.debug("刷新令牌在加锁后验证时已不存在: username={}", username);
                    return null;
                }
                
                // 从JWT中提取用户名进行双重验证
                String usernameFromJWT = BmsJwtUtils.getUsernameFromBmsRefreshToken(refreshToken);
                if (StringUtils.hasText(usernameFromJWT) && usernameFromJWT.equals(revalidatedInfo.getUsername())) {
                    log.debug("BMS JWT Refresh Token验证成功: username={}", revalidatedInfo.getUsername());
                    return revalidatedInfo.getUsername();
                } else {
                    log.debug("从BMS Refresh Token中提取的用户信息不匹配: jwtUsername={}, redisUsername={}", 
                        usernameFromJWT, revalidatedInfo.getUsername());
                    return null;
                }
                
            } finally {
                // 确保锁被释放
                if (mutex != null) {
                    try {
                        boolean unlocked = redisLockService.unlock(mutex);
                        log.debug("释放登录锁{}: username={}", unlocked ? "成功" : "失败", username);
                    } catch (Exception unlockEx) {
                        log.error("释放登录锁异常: username={}, error={}", username, unlockEx.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("JWT Refresh Token验证失败: error={}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 使refresh token失效
     * 使用分布式锁确保与其他认证操作的串行化
     * @param refreshToken refresh token
     * @return 是否成功
     */
    public boolean invalidateRefreshToken(String refreshToken) {
        if (!StringUtils.hasText(refreshToken)) {
            return false;
        }
        
        // 先获取刷新令牌信息以确定用户名（在锁外执行，避免无效token也要获取锁）
        RefreshTokenInfo refreshTokenInfo;
        try {
            refreshTokenInfo = cacheService.getCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken, RefreshTokenInfo.class);
            if (refreshTokenInfo == null) {
                log.debug("刷新令牌已不存在，无需失效: token前缀={}", refreshToken.substring(0, Math.min(20, refreshToken.length())));
                return true; // token不存在视为已失效，返回成功
            }
        } catch (Exception e) {
            log.error("获取刷新令牌信息失败: error={}", e.getMessage(), e);
            return false;
        }
        
        // 使用分布式锁确保与其他认证操作串行化
        String username = refreshTokenInfo.getUsername();
        String lockKey = username;
        Mutex mutex = null;
        
        try {
            // 获取与其他认证操作相同的分布式锁
            mutex = redisLockService.lock("USER_LOGIN_LOCK", lockKey);
            if (mutex == null) {
                log.warn("用户 {} 正在进行认证操作，刷新令牌失效请稍后重试", username);
                return false;
            }
            
            log.debug("获取登录锁成功，开始刷新令牌失效: username={}", username);
            
            // 在锁保护下执行失效操作
            // 再次验证token是否仍然存在（可能在等待锁期间已被其他操作删除）
            RefreshTokenInfo currentTokenInfo = cacheService.getCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken, RefreshTokenInfo.class);
            if (currentTokenInfo != null) {
                // 删除刷新令牌
                cacheService.delCache(REFRESH_TOKEN_CACHE_GROUP, refreshToken);
                log.info("刷新令牌失效成功: username={}, token前缀={}", username, refreshToken.substring(0, Math.min(20, refreshToken.length())));
            } else {
                log.debug("刷新令牌在锁保护下已不存在: username={}", username);
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("刷新令牌失效失败: username={}, error={}", username, e.getMessage(), e);
            return false;
        } finally {
            // 确保锁被释放
            if (mutex != null) {
                try {
                    boolean unlocked = redisLockService.unlock(mutex);
                    log.debug("释放登录锁{}: username={}", unlocked ? "成功" : "失败", username);
                } catch (Exception unlockEx) {
                    log.error("释放登录锁异常: username={}, error={}", username, unlockEx.getMessage());
                }
            }
        }
    }
    
    /**
     * Token信息内部类
     */
    public static class TokenInfo {
        private String token;
        private String username;
        private String userInfo;
        private long createTime;
        private long lastAccessTime;

        // Getters and Setters
        public String getToken() { return token; }
        public void setToken(String token) { this.token = token; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getUserInfo() { return userInfo; }
        public void setUserInfo(String userInfo) { this.userInfo = userInfo; }

        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }

        public long getLastAccessTime() { return lastAccessTime; }
        public void setLastAccessTime(long lastAccessTime) { this.lastAccessTime = lastAccessTime; }
    }
    
    /**
     * Refresh Token信息内部类
     */
    public static class RefreshTokenInfo {
        private String refreshToken;
        private String username;
        private long createTime;
        
        // Getters and Setters
        public String getRefreshToken() { return refreshToken; }
        public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }
    }
}
