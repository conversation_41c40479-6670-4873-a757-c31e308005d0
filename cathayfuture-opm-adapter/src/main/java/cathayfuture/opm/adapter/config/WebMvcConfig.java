package cathayfuture.opm.adapter.config;

import cathayfuture.opm.adapter.ratelimit.RateLimitInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * Web MVC配置
 * 注册限流拦截器
 * 
 * <AUTHOR> <PERSON>
 * @date 2025-08-14
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    private RateLimitInterceptor rateLimitInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(rateLimitInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                    "/actuator/**",  // 排除健康检查接口
                    "/swagger-ui/**", // 排除Swagger接口
                    "/v3/api-docs/**", // 排除API文档接口
                    "/swagger-resources/**",
                    "/webjars/**",
                    "/error"  // 排除错误页面
                );
    }
}