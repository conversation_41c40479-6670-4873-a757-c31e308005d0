package cathayfuture.opm.adapter.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * API限流配置属性
 * 对应application.yml中的cathay.rate-limit配置
 * 
 * <AUTHOR> Code
 * @date 2025-08-14
 */
@Data
@Component
@ConfigurationProperties(prefix = "cathay.rate-limit")
public class RateLimitConfigProperties {

    /**
     * 是否启用限流
     */
    private boolean enabled = true;

    /**
     * 通用API限流配置
     */
    private LimitConfig general = new LimitConfig(100, 60);

    /**
     * 高频API限流配置
     */
    private LimitConfig highFrequency = new LimitConfig(500, 60);

    /**
     * 低频API限流配置
     */
    private LimitConfig lowFrequency = new LimitConfig(20, 60);

    /**
     * 排除路径列表
     */
    private List<String> excludePaths;

    /**
     * 限流配置类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LimitConfig {
        /**
         * 容量（次数）
         */
        private int capacity;
        
        /**
         * 时间窗口（秒）
         */
        private int windowSeconds;
    }

    /**
     * 检查指定URL是否应该被排除
     */
    public boolean shouldSkipUrl(String url) {
        if (excludePaths == null) {
            return false;
        }
        
        return excludePaths.stream()
                .anyMatch(excludePath -> {
                    // 简单的通配符匹配
                    if (excludePath.endsWith("/**")) {
                        String prefix = excludePath.substring(0, excludePath.length() - 3);
                        return url.startsWith(prefix);
                    } else if (excludePath.endsWith("/*")) {
                        String prefix = excludePath.substring(0, excludePath.length() - 2);
                        return url.startsWith(prefix) && !url.substring(prefix.length()).contains("/");
                    } else {
                        return url.equals(excludePath);
                    }
                });
    }
}