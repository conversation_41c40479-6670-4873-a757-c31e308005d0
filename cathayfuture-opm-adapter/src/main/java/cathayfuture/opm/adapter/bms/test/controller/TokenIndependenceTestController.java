package cathayfuture.opm.adapter.bms.test.controller;

import cathayfuture.opm.adapter.mobile.utils.JwtUtils;
import cathayfuture.opm.adapter.security.BmsJwtUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Token系统独立性测试控制器
 * 用于验证BMS token系统与小程序token系统的独立性
 * 除了生产环境外的所有环境都可以访问
 * 
 * ⚠️ 安全提醒：此控制器仅用于开发、测试、UAT等非生产环境
 * 生产环境会自动禁用，防止测试接口暴露
 * 
 * <AUTHOR> Code
 * @date 2025-08-13
 */
@Slf4j
@RestController
@Profile({"!prod"})
@Tag(name = "Token系统独立性测试", description = "验证BMS token与小程序token的独立性")
@RequestMapping("/bms/test/token")
public class TokenIndependenceTestController {

    @GetMapping("/independence")
    @Operation(summary = "Token系统独立性测试", description = "验证BMS token系统与小程序token系统完全独立")
    public Map<String, Object> testTokenIndependence() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始执行Token系统独立性测试");
            
            // 测试1: BMS Token与小程序Token使用不同的密钥和标识
            Map<String, Object> bmsPayload = new HashMap<>();
            bmsPayload.put("username", "test_user");
            bmsPayload.put("userId", 123L);
            
            // 生成BMS tokens
            String bmsAccessToken = BmsJwtUtils.createBmsAccessToken(bmsPayload);
            String bmsRefreshToken = BmsJwtUtils.createBmsRefreshToken(bmsPayload);
            
            // 生成小程序tokens (保持原有格式)
            String miniProgramToken = JwtUtils.createToken("test_user_mini");
            String miniProgramRefreshToken = JwtUtils.createToken("test_user_mini");
            
            result.put("bms_access_token", bmsAccessToken != null ? "生成成功" : "生成失败");
            result.put("bms_refresh_token", bmsRefreshToken != null ? "生成成功" : "生成失败");
            result.put("mini_program_token", miniProgramToken != null ? "生成成功" : "生成失败");
            result.put("mini_program_refresh_token", miniProgramRefreshToken != null ? "生成成功" : "生成失败");
            
            // 测试2: 验证Token类型检测
            result.put("bms_access_token_type", BmsJwtUtils.getBmsTokenType(bmsAccessToken));
            result.put("bms_refresh_token_type", BmsJwtUtils.getBmsTokenType(bmsRefreshToken));

            
            // 测试3: 验证跨系统验证失败（确保独立性）

            boolean miniTokenWithBmsValidator = BmsJwtUtils.verifyBmsAccessToken(miniProgramToken);
            

            result.put("mini_token_with_bms_validator", miniTokenWithBmsValidator);
            
            // 测试4: 验证同系统验证成功
            boolean bmsTokenWithBmsValidator = BmsJwtUtils.verifyBmsAccessToken(bmsAccessToken);
            
            result.put("bms_token_with_bms_validator", bmsTokenWithBmsValidator);

            // 测试5: 验证Token识别
            boolean isBmsToken = BmsJwtUtils.isBmsToken(bmsAccessToken);
            boolean isMiniTokenBms = BmsJwtUtils.isBmsToken(miniProgramToken);
            
            result.put("is_bms_token", isBmsToken);
            result.put("is_mini_token_bms", isMiniTokenBms);
            
            // 测试结果分析
            boolean independenceTestPassed = 
                !miniTokenWithBmsValidator &&  // 小程序token不能被BMS验证器验证
                bmsTokenWithBmsValidator &&    // BMS token能被BMS验证器验证
                isBmsToken &&                  // BMS token被正确识别
                !isMiniTokenBms;               // 小程序token不被识别为BMS token
                
            result.put("independence_test_passed", independenceTestPassed);
            result.put("test_status", "SUCCESS");
            
            log.info("Token系统独立性测试完成，结果: {}", independenceTestPassed ? "PASSED" : "FAILED");
            
        } catch (Exception e) {
            log.error("Token系统独立性测试失败", e);
            result.put("test_status", "ERROR");
            result.put("error_message", e.getMessage());
        }
        
        return result;
    }
    
    @GetMapping("/claims")
    @Operation(summary = "Token声明提取测试", description = "测试BMS token的声明提取功能")
    public Map<String, Object> testTokenClaims() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始执行Token声明提取测试");
            
            // 创建测试用的BMS token
            Map<String, Object> bmsPayload = new HashMap<>();
            bmsPayload.put("username", "test_bms_user");
            bmsPayload.put("userId", 456L);
            bmsPayload.put("department", "IT");
            
            String bmsAccessToken = BmsJwtUtils.createBmsAccessToken(bmsPayload);
            String bmsRefreshToken = BmsJwtUtils.createBmsRefreshToken(bmsPayload);
            
            // 测试BMS token claim提取
            String username = BmsJwtUtils.getUsernameFromBmsAccessToken(bmsAccessToken);
            String userInfo = BmsJwtUtils.getUserInfoFromBmsAccessToken(bmsAccessToken);
            String refreshUsername = BmsJwtUtils.getUsernameFromBmsRefreshToken(bmsRefreshToken);
            
            result.put("token_generated", bmsAccessToken != null ? "成功" : "失败");
            result.put("refresh_token_generated", bmsRefreshToken != null ? "成功" : "失败");
            result.put("extracted_username", username);
            result.put("extracted_user_info", userInfo);
            result.put("refresh_username", refreshUsername);
            
            // 验证提取的信息正确性
            boolean claimsTestPassed = 
                "test_bms_user".equals(username) &&
                "test_bms_user".equals(refreshUsername) &&
                userInfo != null && userInfo.contains("test_bms_user");
                
            result.put("claims_test_passed", claimsTestPassed);
            result.put("test_status", "SUCCESS");
            
            log.info("Token声明提取测试完成，结果: {}", claimsTestPassed ? "PASSED" : "FAILED");
            
        } catch (Exception e) {
            log.error("Token声明提取测试失败", e);
            result.put("test_status", "ERROR");
            result.put("error_message", e.getMessage());
        }
        
        return result;
    }
    
    @GetMapping("/security-validation")
    @Operation(summary = "Token安全验证测试", description = "测试token的安全性和验证机制")
    public Map<String, Object> testSecurityValidation() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始执行Token安全验证测试");
            
            // 测试无效token的处理
            boolean invalidTokenResult1 = BmsJwtUtils.verifyBmsAccessToken("invalid_token");
            boolean invalidTokenResult2 = BmsJwtUtils.verifyBmsRefreshToken("invalid_token");
            // 注意：原始JwtUtils没有verifyToken方法，只能通过decodeToken测试
            String invalidDecodeResult = JwtUtils.decodeToken("invalid_token");

            result.put("invalid_bms_access_token_verification", invalidTokenResult1);
            result.put("invalid_bms_refresh_token_verification", invalidTokenResult2);
            result.put("invalid_mini_token_decode", invalidDecodeResult);
            
            // 测试空token的处理
            String nullTokenUsername1 = BmsJwtUtils.getUsernameFromBmsAccessToken(null);
            String nullTokenUsername2 = BmsJwtUtils.getUsernameFromBmsRefreshToken(null);
            String nullTokenType = BmsJwtUtils.getBmsTokenType(null);
            
            result.put("null_access_token_username", nullTokenUsername1);
            result.put("null_refresh_token_username", nullTokenUsername2);
            result.put("null_token_type", nullTokenType);
            
            // 测试错误格式token的处理
            String emptyTokenUsername = BmsJwtUtils.getUsernameFromBmsAccessToken("");
            boolean emptyTokenVerification = BmsJwtUtils.verifyBmsAccessToken("");
            
            result.put("empty_token_username", emptyTokenUsername);
            result.put("empty_token_verification", emptyTokenVerification);
            
            // 验证安全测试是否通过
            boolean securityTestPassed = 
                !invalidTokenResult1 &&        // 无效token验证失败
                !invalidTokenResult2 &&        // 无效refresh token验证失败
                invalidDecodeResult == null &&  // 无效小程序token解码失败
                nullTokenUsername1 == null &&  // null token返回null
                nullTokenUsername2 == null &&  // null refresh token返回null
                nullTokenType == null &&       // null token类型返回null
                emptyTokenUsername == null &&  // 空token返回null
                !emptyTokenVerification;       // 空token验证失败
                
            result.put("security_test_passed", securityTestPassed);
            result.put("test_status", "SUCCESS");
            
            log.info("Token安全验证测试完成，结果: {}", securityTestPassed ? "PASSED" : "FAILED");
            
        } catch (Exception e) {
            log.error("Token安全验证测试失败", e);
            result.put("test_status", "ERROR");
            result.put("error_message", e.getMessage());
        }
        
        return result;
    }
}