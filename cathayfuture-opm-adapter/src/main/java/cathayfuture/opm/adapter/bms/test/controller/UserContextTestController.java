package cathayfuture.opm.adapter.bms.test.controller;

import cathayfuture.opm.infra.common.ExtServiceContext;
import cathayfuture.opm.infra.common.LocalServiceContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户上下文测试接口
 * 用于验证ExtServiceContext和LocalServiceContext的各个方法
 * 除了生产环境外的所有环境都可以访问
 * 
 * ⚠️ 安全提醒：此控制器仅用于开发、测试、UAT等非生产环境
 * 生产环境会自动禁用，防止测试接口暴露
 * 
 * <AUTHOR>
 * @since 2025-08-12
 */
@Slf4j
@RestController
@Profile({"!prod"})
@Tag(name = "用户上下文测试", description = "测试用户上下文信息获取")
@RequestMapping("/bms/test/userContext")
public class UserContextTestController {

    @GetMapping("/extServiceContext")
    @Operation(summary = "测试ExtServiceContext各个方法", description = "验证ExtServiceContext是否返回正确的用户信息")
    public Map<String, Object> testExtServiceContext() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取Spring Security认证信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            result.put("authentication", auth != null ? auth.getName() : "null");
            result.put("isAuthenticated", auth != null && auth.isAuthenticated());
            result.put("principal", auth != null ? auth.getPrincipal().toString() : "null");
            result.put("authorities", auth != null ? auth.getAuthorities().toString() : "null");
            
            // 测试ExtServiceContext各个方法
            result.put("roleId", ExtServiceContext.getRoleId());
            result.put("userCode", ExtServiceContext.getUserCode());
            result.put("userName", ExtServiceContext.getUserName());
            result.put("postCode", ExtServiceContext.getPostCode());
            result.put("postName", ExtServiceContext.getPostName());
            
            // 测试header key生成
            result.put("roleIdHeaderKey", ExtServiceContext.getHeaderKey(ExtServiceContext.ROLE_ID));
            result.put("userCodeHeaderKey", ExtServiceContext.getHeaderKey(ExtServiceContext.USER_CODE));
            
            result.put("status", "SUCCESS");
            result.put("message", "ExtServiceContext测试完成");
            
        } catch (Exception e) {
            log.error("ExtServiceContext测试失败", e);
            result.put("status", "ERROR");
            result.put("message", "测试失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }
    
    @GetMapping("/localServiceContext")
    @Operation(summary = "测试LocalServiceContext各个方法", description = "验证LocalServiceContext是否返回正确的用户信息")
    public Map<String, Object> testLocalServiceContext() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            LocalServiceContext context = LocalServiceContext.getContext();
            
            // 测试LocalServiceContext各个方法
            result.put("requestUserId", context.getRequestUserId());
            result.put("requestUserCode", context.getRequestUserCode());
            result.put("requestTenantIdString", context.getRequestTenantIdString());
            result.put("requestInstanceId", context.getRequestInstanceId());
            
            // 测试get方法
            result.put("roleIdFromGet", context.get(ExtServiceContext.ROLE_ID));
            result.put("userCodeFromGet", context.get(ExtServiceContext.USER_CODE));
            result.put("userNameFromGet", context.get(ExtServiceContext.USER_NAME));
            result.put("postCodeFromGet", context.get(ExtServiceContext.POST_CODE));
            result.put("postNameFromGet", context.get(ExtServiceContext.POST_NAME));
            result.put("userIdFromGet", context.get(ExtServiceContext.USER_ID));
            
            result.put("status", "SUCCESS");
            result.put("message", "LocalServiceContext测试完成");
            
        } catch (Exception e) {
            log.error("LocalServiceContext测试失败", e);
            result.put("status", "ERROR");
            result.put("message", "测试失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }
    
    @GetMapping("/compatibility")
    @Operation(summary = "兼容性测试", description = "测试新旧上下文的兼容性")
    public Map<String, Object> testCompatibility() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            LocalServiceContext localContext = LocalServiceContext.getContext();
            
            // 比较ExtServiceContext和LocalServiceContext的结果
            Map<String, Object> extResults = new HashMap<>();
            extResults.put("roleId", ExtServiceContext.getRoleId());
            extResults.put("userCode", ExtServiceContext.getUserCode());
            extResults.put("userName", ExtServiceContext.getUserName());
            extResults.put("postCode", ExtServiceContext.getPostCode());
            extResults.put("postName", ExtServiceContext.getPostName());
            
            Map<String, Object> localResults = new HashMap<>();
            localResults.put("roleId", localContext.get(ExtServiceContext.ROLE_ID));
            localResults.put("userCode", localContext.get(ExtServiceContext.USER_CODE));
            localResults.put("userName", localContext.get(ExtServiceContext.USER_NAME));
            localResults.put("postCode", localContext.get(ExtServiceContext.POST_CODE));
            localResults.put("postName", localContext.get(ExtServiceContext.POST_NAME));
            localResults.put("userId", localContext.getRequestUserId());
            localResults.put("userCodeDirect", localContext.getRequestUserCode());
            
            result.put("extServiceContext", extResults);
            result.put("localServiceContext", localResults);
            
            // 检查一致性
            boolean consistent = true;
            StringBuilder inconsistencies = new StringBuilder();
            
            if (!equals(extResults.get("roleId"), localResults.get("roleId"))) {
                consistent = false;
                inconsistencies.append("roleId不一致; ");
            }
            if (!equals(extResults.get("userCode"), localResults.get("userCode"))) {
                consistent = false;
                inconsistencies.append("userCode不一致; ");
            }
            if (!equals(extResults.get("userName"), localResults.get("userName"))) {
                consistent = false;
                inconsistencies.append("userName不一致; ");
            }
            if (!equals(extResults.get("postCode"), localResults.get("postCode"))) {
                consistent = false;
                inconsistencies.append("postCode不一致; ");
            }
            if (!equals(extResults.get("postName"), localResults.get("postName"))) {
                consistent = false;
                inconsistencies.append("postName不一致; ");
            }
            
            result.put("consistent", consistent);
            result.put("inconsistencies", inconsistencies.toString());
            result.put("status", "SUCCESS");
            result.put("message", consistent ? "兼容性测试通过" : "发现不一致: " + inconsistencies.toString());
            
        } catch (Exception e) {
            log.error("兼容性测试失败", e);
            result.put("status", "ERROR");
            result.put("message", "测试失败: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }
        
        return result;
    }
    
    private boolean equals(Object obj1, Object obj2) {
        if (obj1 == null && obj2 == null) return true;
        if (obj1 == null || obj2 == null) return false;
        return obj1.equals(obj2);
    }
}
