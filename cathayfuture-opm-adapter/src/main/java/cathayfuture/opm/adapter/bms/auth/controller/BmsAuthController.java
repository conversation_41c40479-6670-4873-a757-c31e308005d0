package cathayfuture.opm.adapter.bms.auth.controller;

import cathayfuture.opm.adapter.bms.auth.dto.BmsLoginRequest;
import cathayfuture.opm.adapter.bms.auth.dto.BmsLoginResponse;
import cathayfuture.opm.adapter.ratelimit.RateLimit;
import cathayfuture.opm.adapter.security.BmsJwtUtils;
import cathayfuture.opm.adapter.security.LoginRateLimiter;
import cathayfuture.opm.adapter.security.TokenManagementService;
import cathayfuture.opm.adapter.security.DatabaseUserDetailsService;
import com.alibaba.fastjson.JSON;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/bms/auth")
@RateLimit(RateLimit.LimitType.GENERAL)
public class BmsAuthController {

    private final AuthenticationManager authenticationManager;

    @Resource
    private TokenManagementService tokenManagementService;
    
    @Resource
    private DatabaseUserDetailsService userDetailsService;
    
    @Resource
    private LoginRateLimiter rateLimiter;

    public BmsAuthController(AuthenticationManager authenticationManager) {
        this.authenticationManager = authenticationManager;
    }
    
    @PostMapping("/login")
    public ResponseEntity<BmsLoginResponse> login(@Valid @RequestBody BmsLoginRequest request, HttpServletRequest httpRequest) {
        // 检查速率限制
        if (!rateLimiter.allowLoginAttempt(httpRequest, request.getUsername())) {
            long remainingTime = rateLimiter.getRemainingLockTime(httpRequest, request.getUsername());
            String message = String.format("登录尝试过于频繁，请在 %d 秒后重试", remainingTime);
            return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                .body(BmsLoginResponse.error(message));
        }
        
        try {
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
            );
            
            // 认证成功，记录成功登录
            rateLimiter.recordLoginSuccess(httpRequest, request.getUsername());
            
            // 获取完整用户信息
            Map<String, Object> userInfo = userDetailsService.getUserInfoByUsername(request.getUsername());
            if (userInfo == null) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BmsLoginResponse.error("获取用户信息失败"));
            }
            
            // 创建token payload（包含完整用户信息）
            Map<String, Object> payload = new HashMap<>(userInfo);
            payload.put("authorities", getAuthorities(authentication));
            
            // 生成BMS JWT token
            String token = BmsJwtUtils.createBmsAccessToken(payload);

            // 将token存储到Redis
            String userInfoJson = JSON.toJSONString(payload);
            boolean stored = tokenManagementService.storeToken(token, request.getUsername(), userInfoJson);

            if (!stored) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(BmsLoginResponse.error("登录失败，请重试"));
            }

            // 生成refresh token
            String refreshToken = tokenManagementService.generateRefreshToken(request.getUsername());

            return ResponseEntity.ok(BmsLoginResponse.builder()
                .token(token)
                .refreshToken(refreshToken)
                .username(request.getUsername())
                .authorities(getAuthorities(authentication))
                .build());
                
        } catch (AuthenticationException e) {
            // 认证失败，记录失败尝试
            rateLimiter.recordLoginFailure(httpRequest, request.getUsername());
            
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(BmsLoginResponse.error("用户名或密码错误"));
        }
    }
    
    @PostMapping("/logout")
    public ResponseEntity<Void> logout(HttpServletRequest request) {
        // 从请求中提取token
        String token = extractTokenFromRequest(request);

        if (token != null) {
            // 从Redis中删除token
            boolean invalidated = tokenManagementService.invalidateToken(token);
            if (invalidated) {
                SecurityContextHolder.clearContext();
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        }

        SecurityContextHolder.clearContext();
        return ResponseEntity.ok().build();
    }

    @PostMapping("/refresh")
    public ResponseEntity<BmsLoginResponse> refreshToken(@RequestBody Map<String, String> request) {
        String refreshToken = request.get("refreshToken");

        if (refreshToken == null) {
            return ResponseEntity.badRequest()
                .body(BmsLoginResponse.error("Refresh token不能为空"));
        }

        // 验证refresh token
        String username = tokenManagementService.validateRefreshToken(refreshToken);
        if (username == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(BmsLoginResponse.error("Refresh token无效或已过期"));
        }

        // 重新从数据库加载用户权限
        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        List<String> userAuthorities = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        
        // 获取真实用户ID
        Long userId = userDetailsService.getUserIdByUsername(username);
        if (userId == null) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(BmsLoginResponse.error("获取用户信息失败"));
        }
        
        // 生成新的access token
        Map<String, Object> payload = new HashMap<>();
        payload.put("username", username);
        payload.put("userId", userId);
        payload.put("authorities", userAuthorities);

        String newToken = BmsJwtUtils.createBmsAccessToken(payload);
        String userInfoJson = JSON.toJSONString(payload);

        // 存储新token到Redis
        boolean stored = tokenManagementService.storeToken(newToken, username, userInfoJson);
        if (!stored) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(BmsLoginResponse.error("Token刷新失败"));
        }

        // 生成新的refresh token
        String newRefreshToken = tokenManagementService.generateRefreshToken(username);

        // 使旧的refresh token失效
        tokenManagementService.invalidateRefreshToken(refreshToken);

        return ResponseEntity.ok(BmsLoginResponse.builder()
            .token(newToken)
            .refreshToken(newRefreshToken)
            .username(username)
            .authorities(userAuthorities)
            .build());
    }

    /**
     * 从请求中提取token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String header = request.getHeader("Authorization");
        if (header != null && header.startsWith("Bearer ")) {
            return header.substring(7);
        }
        return header;
    }
    
    private List<String> getAuthorities(Authentication authentication) {
        return authentication.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority)
            .collect(Collectors.toList());
    }
}