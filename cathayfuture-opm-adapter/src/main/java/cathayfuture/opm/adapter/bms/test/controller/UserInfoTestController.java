package cathayfuture.opm.adapter.bms.test.controller;

import cathayfuture.opm.adapter.ratelimit.RateLimit;
import cathayfuture.opm.adapter.security.AuthenticatedUserService;
import cathayfuture.opm.infra.common.ExtServiceContext;
import cathayfuture.opm.infra.common.LocalServiceContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户信息测试控制器
 * 用于验证JWT token中的用户信息提取功能
 *
 * <AUTHOR> Code
 * @date 2025-08-14
 */
@RestController
@RequestMapping("/bms/test")
public class UserInfoTestController {

    @Resource
    private AuthenticatedUserService authenticatedUserService;

    /**
     * 测试从JWT token获取用户信息
     */
    @GetMapping("/user-info")
    @RateLimit(RateLimit.LimitType.HIGH_FREQUENCY)
    public Map<String, Object> getUserInfo() {
        Map<String, Object> result = new HashMap<>();
        
        // 从AuthenticatedUserService获取
        Map<String, Object> serviceInfo = new HashMap<>();
        serviceInfo.put("hasAuthenticatedUser", authenticatedUserService.hasAuthenticatedUser());
        serviceInfo.put("username", authenticatedUserService.getCurrentUsername());
        serviceInfo.put("userId", authenticatedUserService.getCurrentUserId());
        serviceInfo.put("realName", authenticatedUserService.getCurrentUserRealName());
        serviceInfo.put("roleIds", authenticatedUserService.getCurrentUserRoleIds());
        serviceInfo.put("postCode", authenticatedUserService.getCurrentUserPostCode());
        serviceInfo.put("postName", authenticatedUserService.getCurrentUserPostName());
        result.put("authenticatedUserService", serviceInfo);
        
        // 从ExtServiceContext获取
        Map<String, Object> extContextInfo = new HashMap<>();
        extContextInfo.put("roleId", ExtServiceContext.getRoleId());
        extContextInfo.put("userCode", ExtServiceContext.getUserCode());
        extContextInfo.put("userName", ExtServiceContext.getUserName());
        extContextInfo.put("postCode", ExtServiceContext.getPostCode());
        extContextInfo.put("postName", ExtServiceContext.getPostName());
        result.put("extServiceContext", extContextInfo);
        
        // 从LocalServiceContext获取
        LocalServiceContext localContext = LocalServiceContext.getContext();
        Map<String, Object> localContextInfo = new HashMap<>();
        localContextInfo.put("requestUserId", localContext.getRequestUserId());
        localContextInfo.put("requestUserCode", localContext.getRequestUserCode());
        localContextInfo.put("requestTenantIdString", localContext.getRequestTenantIdString());
        localContextInfo.put("requestInstanceId", localContext.getRequestInstanceId());
        result.put("localServiceContext", localContextInfo);
        
        // 完整的用户信息（调试用）
        result.put("currentUserInfoDebug", authenticatedUserService.getCurrentUserInfo());
        
        return result;
    }

    /**
     * 测试静态方法
     */
    @GetMapping("/static-methods")
    @RateLimit(RateLimit.LimitType.GENERAL)
    public Map<String, Object> testStaticMethods() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("authenticatedUsername", AuthenticatedUserService.getAuthenticatedUsername());
        result.put("authenticatedUserId", AuthenticatedUserService.getAuthenticatedUserId());
        result.put("authenticatedUserRealName", AuthenticatedUserService.getAuthenticatedUserRealName());
        result.put("authenticatedUserRoleIds", AuthenticatedUserService.getAuthenticatedUserRoleIds());
        result.put("authenticatedUserPostCode", AuthenticatedUserService.getAuthenticatedUserPostCode());
        result.put("authenticatedUserPostName", AuthenticatedUserService.getAuthenticatedUserPostName());
        
        return result;
    }
}