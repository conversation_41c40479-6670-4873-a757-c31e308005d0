package cathayfuture.opm.adapter.bms.test.controller;

import cathayfuture.opm.adapter.ratelimit.RateLimitService;
import cathayfuture.opm.adapter.ratelimit.RateLimit;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * API限流测试控制器
 * 除了生产环境外的所有环境都可以访问
 * 
 * ⚠️ 安全提醒：此控制器仅用于开发、测试、UAT等非生产环境
 * 生产环境会自动禁用，防止测试接口暴露
 * 
 * <AUTHOR> Code
 * @since 2025-08-14
 */
@Slf4j
@RestController
@RequestMapping("/bms/test/api-rate-limit")
@Profile({"!prod"})
@Tag(name = "API限流测试", description = "测试通用API限流功能（非生产环境）")
public class ApiRateLimitTestController {

    @Resource
    private RateLimitService rateLimitService;

    /**
     * 测试通用API限流：100次/分钟
     */
    @Operation(summary = "测试通用API限流", description = "限制：100次/分钟")
    @GetMapping("/general")
    @RateLimit(RateLimit.LimitType.GENERAL)
    public Map<String, Object> testGeneralRateLimit(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        result.put("type", "通用API限流");
        result.put("limit", "100次/分钟");
        result.put("message", "请求成功");
        result.put("availableTokens", rateLimitService.getAvailableTokens(request, "/bms/test/api-rate-limit/general", "general"));
        return result;
    }

    /**
     * 测试高频API限流：500次/分钟
     */
    @Operation(summary = "测试高频API限流", description = "限制：500次/分钟")
    @GetMapping("/high-frequency")
    @RateLimit(RateLimit.LimitType.HIGH_FREQUENCY)
    public Map<String, Object> testHighFrequencyRateLimit(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        result.put("type", "高频API限流");
        result.put("limit", "500次/分钟");
        result.put("message", "请求成功");
        result.put("availableTokens", rateLimitService.getAvailableTokens(request, "/bms/test/api-rate-limit/high-frequency", "high"));
        return result;
    }

    /**
     * 测试低频API限流：20次/分钟
     */
    @Operation(summary = "测试低频API限流", description = "限制：20次/分钟")
    @PostMapping("/low-frequency")
    @RateLimit(RateLimit.LimitType.LOW_FREQUENCY)
    public Map<String, Object> testLowFrequencyRateLimit(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        result.put("type", "低频API限流");
        result.put("limit", "20次/分钟");
        result.put("message", "请求成功");
        result.put("availableTokens", rateLimitService.getAvailableTokens(request, "/bms/test/api-rate-limit/low-frequency", "low"));
        return result;
    }

    /**
     * 测试类级别的限流注解
     */
    @Operation(summary = "测试类级别限流", description = "继承类级别的限流配置")
    @GetMapping("/class-level")
    public Map<String, Object> testClassLevelRateLimit(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>();
        result.put("type", "类级别限流");
        result.put("message", "如果类上有@RateLimit注解，则会应用该限流配置");
        return result;
    }

    /**
     * 测试无限流的接口
     */
    @Operation(summary = "测试无限流接口", description = "没有@RateLimit注解，不进行限流")
    @GetMapping("/no-limit")
    public Map<String, Object> testNoRateLimit() {
        Map<String, Object> result = new HashMap<>();
        result.put("type", "无限流");
        result.put("message", "此接口没有限流配置");
        return result;
    }

//    /**
//     * 获取限流状态信息
//     */
//    @Operation(summary = "获取限流状态", description = "查看当前IP在不同限流策略下的状态")
//    @GetMapping("/status")
//    public Map<String, Object> getRateLimitStatus(HttpServletRequest request) {
//        Map<String, Object> result = new HashMap<>();
//
//        result.put("general", Map.of(
//            "availableTokens", rateLimitService.getAvailableTokens(request, "/bms/test/api-rate-limit/general", "general"),
//            "limit", "100次/分钟"
//        ));
//
//        result.put("highFrequency", Map.of(
//            "availableTokens", rateLimitService.getAvailableTokens(request, "/bms/test/api-rate-limit/high-frequency", "high"),
//            "limit", "500次/分钟"
//        ));
//
//        result.put("lowFrequency", Map.of(
//            "availableTokens", rateLimitService.getAvailableTokens(request, "/bms/test/api-rate-limit/low-frequency", "low"),
//            "limit", "20次/分钟"
//        ));
//
//        return result;
//    }
}