package cathayfuture.opm.adapter.ratelimit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * API限流拦截器
 * 基于注解自动进行限流检查
 * 
 * <AUTHOR> Code
 * @date 2025-08-14
 */
@Slf4j
@Component
public class RateLimitInterceptor implements HandlerInterceptor {

    @Resource
    private RateLimitService rateLimitService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        log.debug("RateLimitInterceptor - 处理请求: {} from IP: {}", apiPath, maskIp(clientIp));
        
        // 检查限流功能是否启用
        if (!rateLimitService.isEnabled()) {
            log.debug("RateLimitInterceptor - 限流功能已禁用，放行请求: {}", apiPath);
            return true;
        }
        
        // 只处理Controller方法
        if (!(handler instanceof HandlerMethod)) {
            log.debug("RateLimitInterceptor - 非Controller方法，放行请求: {}", apiPath);
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        
        // 检查方法上的注解
        RateLimit methodRateLimit = handlerMethod.getMethodAnnotation(RateLimit.class);
        // 检查类上的注解
        RateLimit classRateLimit = handlerMethod.getBeanType().getAnnotation(RateLimit.class);
        
        // 方法注解优先于类注解
        RateLimit rateLimit = methodRateLimit != null ? methodRateLimit : classRateLimit;
        
        if (rateLimit == null) {
            log.debug("RateLimitInterceptor - 无限流注解，放行请求: {}", apiPath);
            return true; // 没有限流注解，放行
        }

        log.debug("RateLimitInterceptor - 发现限流注解 {}, 检查限流: {}", rateLimit.value(), apiPath);
        boolean allowed = checkRateLimit(request, apiPath, rateLimit.value());
        
        if (!allowed) {
            log.warn("RateLimitInterceptor - 限流触发，拒绝请求: {} from IP: {}", apiPath, maskIp(clientIp));
            handleRateLimitExceeded(response);
            return false;
        }
        
        log.debug("RateLimitInterceptor - 限流检查通过，允许请求: {}", apiPath);
        return true;
    }

    /**
     * 根据限流类型检查是否允许访问
     */
    private boolean checkRateLimit(HttpServletRequest request, String apiPath, RateLimit.LimitType limitType) {
        switch (limitType) {
            case HIGH_FREQUENCY:
                return rateLimitService.allowHighFrequencyApi(request, apiPath);
            case LOW_FREQUENCY:
                return rateLimitService.allowLowFrequencyApi(request, apiPath);
            case GENERAL:
            default:
                return rateLimitService.allowGeneralApi(request, apiPath);
        }
    }

    /**
     * 处理限流超限情况
     */
    private void handleRateLimitExceeded(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"code\":429,\"message\":\"访问过于频繁，请稍后重试\",\"data\":null}");
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个代理的情况，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }

    /**
     * IP地址脱敏
     */
    private String maskIp(String ip) {
        if (ip == null || ip.length() < 7) {
            return "***";
        }
        
        // IPv4脱敏：保留前两段，后两段用*代替
        if (ip.contains(".")) {
            String[] parts = ip.split("\\.");
            if (parts.length >= 4) {
                return parts[0] + "." + parts[1] + ".***.***.";
            }
        }
        
        // IPv6或其他格式的简单脱敏
        return ip.substring(0, Math.min(4, ip.length())) + "***";
    }
}