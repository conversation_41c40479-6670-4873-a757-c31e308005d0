package cathayfuture.opm.adapter.ratelimit;

import cathayfuture.opm.adapter.config.RateLimitConfigProperties;
import com.dtyunxi.huieryun.cache.api.IRedisCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 通用限流服务
 * 基于Redis实现的分布式限流（使用项目现有的Redis缓存服务）
 * 
 * <AUTHOR> Code
 * @date 2025-08-14
 */
@Slf4j
@Service
public class RateLimitService {

    // 缓存组名常量
    private static final String RATE_LIMIT_GROUP = "API_RATE_LIMIT";

    @Resource
    private IRedisCacheService cacheService;
    
    @Resource
    private RateLimitConfigProperties rateLimitConfig;

    /**
     * 是否启用限流功能
     */
    public boolean isEnabled() {
        return rateLimitConfig.isEnabled();
    }

    /**
     * 是否应该对指定URL应用限流
     */
    public boolean shouldApplyLimit(String url) {
        if (!isEnabled()) {
            return false;
        }
        return !rateLimitConfig.shouldSkipUrl(url);
    }

    /**
     * 是否应该对指定URL应用高频限流
     */
    public boolean shouldApplyHighFrequencyLimit(String url) {
        if (!isEnabled()) {
            return false;
        }
        return !rateLimitConfig.shouldSkipUrl(url);
    }

    /**
     * 是否应该对指定URL应用低频限流
     */
    public boolean shouldApplyLowFrequencyLimit(String url) {
        if (!isEnabled()) {
            return false;
        }
        return !rateLimitConfig.shouldSkipUrl(url);
    }

    /**
     * 检查通用API是否允许访问（从配置文件读取限制）
     * 
     * @param request HTTP请求
     * @param apiPath API路径
     * @return 是否允许访问
     */
    public boolean allowGeneralApi(HttpServletRequest request, String apiPath) {
        if (!isEnabled() || !shouldApplyLimit(apiPath)) {
            return true;
        }
        
        RateLimitConfigProperties.LimitConfig config = rateLimitConfig.getGeneral();
        return checkRateLimit(request, apiPath, "general", config.getCapacity(), config.getWindowSeconds());
    }

    /**
     * 检查高频API是否允许访问（从配置文件读取限制）
     * 
     * @param request HTTP请求
     * @param apiPath API路径
     * @return 是否允许访问
     */
    public boolean allowHighFrequencyApi(HttpServletRequest request, String apiPath) {
        if (!isEnabled() || !shouldApplyHighFrequencyLimit(apiPath)) {
            return true;
        }
        
        RateLimitConfigProperties.LimitConfig config = rateLimitConfig.getHighFrequency();
        return checkRateLimit(request, apiPath, "high", config.getCapacity(), config.getWindowSeconds());
    }

    /**
     * 检查低频API是否允许访问（从配置文件读取限制）
     * 
     * @param request HTTP请求
     * @param apiPath API路径
     * @return 是否允许访问
     */
    public boolean allowLowFrequencyApi(HttpServletRequest request, String apiPath) {
        if (!isEnabled() || !shouldApplyLowFrequencyLimit(apiPath)) {
            return true;
        }
        
        RateLimitConfigProperties.LimitConfig config = rateLimitConfig.getLowFrequency();
        return checkRateLimit(request, apiPath, "low", config.getCapacity(), config.getWindowSeconds());
    }

    /**
     * 获取剩余令牌数
     * 
     * @param request HTTP请求
     * @param apiPath API路径
     * @param limitType 限流类型
     * @return 剩余令牌数
     */
    public long getAvailableTokens(HttpServletRequest request, String apiPath, String limitType) {
        String key = buildApiKey(request, apiPath, limitType);
        Integer currentCount = cacheService.getCache(RATE_LIMIT_GROUP, key, Integer.class);
        
        int maxLimit = getMaxLimit(limitType);
        int usedCount = (currentCount == null) ? 0 : currentCount;
        
        return Math.max(0, maxLimit - usedCount);
    }

    /**
     * 限流检查核心逻辑
     */
    private boolean checkRateLimit(HttpServletRequest request, String apiPath, String limitType, int maxCount, int windowSeconds) {
        String key = buildApiKey(request, apiPath, limitType);
        
        // 获取当前计数
        Integer currentCount = cacheService.getCache(RATE_LIMIT_GROUP, key, Integer.class);
        int count = (currentCount == null) ? 1 : currentCount + 1;

        // 检查是否超过限制
        if (count > maxCount) {
            log.warn("{}API限流触发 - IP: {}, API: {}, 当前次数: {}/{}", 
                getLimitTypeName(limitType), maskIp(getClientIp(request)), apiPath, count, maxCount);
            return false;
        }

        // 设置计数，过期时间为窗口时间
        cacheService.setCache(RATE_LIMIT_GROUP, key, count, windowSeconds);
        
        log.debug("{}API访问记录 - IP: {}, API: {}, 当前次数: {}/{}", 
            getLimitTypeName(limitType), maskIp(getClientIp(request)), apiPath, count, maxCount);
        
        return true;
    }

    /**
     * 获取最大限制数
     */
    private int getMaxLimit(String limitType) {
        switch (limitType.toLowerCase()) {
            case "high":
                return rateLimitConfig.getHighFrequency().getCapacity();
            case "low":
                return rateLimitConfig.getLowFrequency().getCapacity();
            case "general":
            default:
                return rateLimitConfig.getGeneral().getCapacity();
        }
    }

    /**
     * 获取限流类型名称
     */
    private String getLimitTypeName(String limitType) {
        switch (limitType.toLowerCase()) {
            case "high":
                return "高频";
            case "low":
                return "低频";
            case "general":
            default:
                return "通用";
        }
    }

    /**
     * 构建限流键
     */
    private String buildApiKey(HttpServletRequest request, String apiPath, String limitType) {
        String clientIp = getClientIp(request);
        return String.format("%s:%s:%s", limitType, clientIp, apiPath);
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个代理的情况，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }

    /**
     * IP地址脱敏
     */
    private String maskIp(String ip) {
        if (ip == null || ip.length() < 7) {
            return "***";
        }
        
        // IPv4脱敏：保留前两段，后两段用*代替
        if (ip.contains(".")) {
            String[] parts = ip.split("\\.");
            if (parts.length >= 4) {
                return parts[0] + "." + parts[1] + ".***.***.";
            }
        }
        
        // IPv6或其他格式的简单脱敏
        return ip.substring(0, Math.min(4, ip.length())) + "***";
    }
}