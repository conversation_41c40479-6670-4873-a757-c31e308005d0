package cathayfuture.opm.adapter.ratelimit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * API限流注解
 * 用于标记需要限流的API接口
 * 
 * <AUTHOR> Code
 * @date 2025-08-14
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    
    /**
     * 限流类型
     */
    LimitType value() default LimitType.GENERAL;
    
    /**
     * 限流类型枚举
     */
    enum LimitType {
        /**
         * 通用API限流：100次/分钟
         */
        GENERAL,
        
        /**
         * 高频API限流：500次/分钟（如查询接口）
         */
        HIGH_FREQUENCY,
        
        /**
         * 低频API限流：20次/分钟（如数据修改接口）
         */
        LOW_FREQUENCY
    }
}