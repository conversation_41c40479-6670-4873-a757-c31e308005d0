management.health.redis.enabled: false
spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************
    username: opm
    password: SH6RzSa#
  main:
    allow-bean-definition-overriding: true


  jackson:
    serialization:
      write-dates-as-timestamps: true
    deserialization:
      fail_on_unknown_properties: false

huieryun.cacheregistryvo:
  # port: 6379
  appId: ""
  livetime: 86370
  # host: *********
  addresses: **********:6379,**********:6379,**********:6379,**********:6379,**********:6379,**********:6379
  workModel: cluster
  appSecret: "Tasly_01!"
  type: REDIS

huieryun.service.lock.lockregistryvo:
  provider: redis
  endpoints: **********:6379,**********:6379,**********:6379,**********:6379,**********:6379,**********:6379
  workModel: cluster
  passwd: Tasly_01!

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

wx:
  miniapp:
    configs:
      - appid: wxb077691a604c20e6
        secret: ab4820f3df719d3b7e563e6880b79d8c
        token:
        aesKey:
        msgDataFormat: JSON

payment-center:
  user-no: CATHAYFUTURE
  pay-product-code: CATHAYFUTURE_PAYMENT
  pay-type-code: WX_JSAPI
  pay-way-code: WX
  pre-pay-url: https://mpgateway.tasly.com/pay-center/payment/pre-order
  notify-url: https://mpgateway.tasly.com/api/cathayfuture/mobile/callbacks/pay/notify
  query-pay-result-url: https://mpgateway.tasly.com/pay-center/payment/wx/query-order/{tenantNo}/{tenantOrderNo}

huieryun.ossregistryvo:
  ossType: AmazonS3
  endpoint: https://tsywisdom.i.tasly.com
  accessKeyId: dHN5d2lzZG9t
  accessKeySecret: bb6b96539b57b2279b5f02f0f0308a99
  acl: public-read
  extFields: {ossTenant: tsywisdom, anonUrl: https://api.i.tasly.com/swift/v1}
  bucketName: prod
  extProperty: {prod: prod}
  dir: cathayfuture-opm/
huieryun.ossappvo:
  appId: tsywisdom


sms:
  ali:
    accessKeyId: LTAI4FzjFk5UNDpRZH3AUKbh
    accessKeySecret: ******************************
    signName: 天人智慧
    templateCode: SMS_170775265
    templateParamName: code
  send:
    profiles: prd, prod

order:
  code:
    prefix: CF
  frontMoney:
    amount: 3000


ribbon:
  ReadTimeout: 200000
  ConnectTimeout: 200000
  ServerListRefreshInterval: 30000

# 固定用户配置
cathay:
  security:
    debug:
      # 生产环境禁用详细认证日志
      enable-logging-provider: false
    dev-mode: false  # 生产模式：启用完整认证，禁用Swagger
    # Token管理策略
    token:
      # 单用户单Token策略（推荐启用以防止Token累积）
      single-token-per-user: ${SINGLE_TOKEN_PER_USER:true}
    jwt:
      bms:
        # JWT密钥（生产环境必须通过环境变量或配置中心设置）
        secret: ${BMS_JWT_SECRET:/oyh2R2whl+u0yiC/9Sa3WW/KW61TGocP5ja4+a93q6rt/xJPRJwcvzgTf3lI72YN/BwfN1Jh8GRymsh3gQtrA==}
        # Access Token有效期（小时）
        access-token-hours: ${BMS_JWT_ACCESS_TOKEN_HOURS:24}
        # Refresh Token有效期（小时）
        refresh-token-hours: ${BMS_JWT_REFRESH_TOKEN_HOURS:48}
        # JWT发行人
        issuer: ${BMS_JWT_ISSUER:CATHAY-FUTURE-BMS-SYSTEM}
        # JWT受众
        audience: ${BMS_JWT_AUDIENCE:CATHAY-FUTURE-BMS-SYSTEM}
    # 登录速率限制配置
    login-rate-limit:
      ip:
        # 每个IP每分钟最多尝试次数
        max-attempts: ${LOGIN_RATE_LIMIT_IP_MAX_ATTEMPTS:10}
        # IP限制时间窗口（分钟）
        window-minutes: ${LOGIN_RATE_LIMIT_IP_WINDOW_MINUTES:1}
        # IP锁定时间（分钟）
        lockout-minutes: ${LOGIN_RATE_LIMIT_IP_LOCKOUT_MINUTES:30}
      username:
        # 每个用户名每小时最多尝试次数
        max-attempts: ${LOGIN_RATE_LIMIT_USERNAME_MAX_ATTEMPTS:30}
        # 用户名限制时间窗口（小时）
        window-hours: ${LOGIN_RATE_LIMIT_USERNAME_WINDOW_HOURS:1}
        # 用户名锁定时间（小时）
        lockout-hours: ${LOGIN_RATE_LIMIT_USERNAME_LOCKOUT_HOURS:24}
  # 认证用户上下文回退配置（仅用于非认证场景）
  auth:
    username: system
    user-id: 0
    role-ids: "0"
    user-name: "系统"
    post-code: "SYSTEM"
    post-name: "系统"

  rate-limit:
    enabled: true
    # 通用API限流：50次/分钟（生产环境严格）
    general:
      capacity: 50
      window-seconds: 60
    # 高频API限流：300次/分钟（移动端接口）
    high-frequency:
      capacity: 300
      window-seconds: 60
    # 低频API限流：10次/分钟（管理员操作）
    low-frequency:
      capacity: 10
      window-seconds: 60
    # 排除路径
    exclude-paths:
      - /bms/auth/login
      - /actuator/**
      - /health
      - /metrics

