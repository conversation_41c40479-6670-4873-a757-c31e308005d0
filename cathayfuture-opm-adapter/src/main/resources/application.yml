spring:
  profiles:
    # active: test  # 注释掉，允许命令行指定
  application:
    name: cathayfuture-opm
  main:
    allow-bean-definition-overriding: true

server:
  port: 8080


# 华夏未来OPM系统安全配置
cathay:
  security:
    jwt:
      bms:
        # JWT密钥（生产环境必须通过环境变量或配置中心设置）
        secret: ${BMS_JWT_SECRET:Q0FUSEFZLUZVVFVSRS1CTVMtU1lTVEVNLVNFQ1VSSVRZPQ==}
        # Access Token有效期（小时）
        access-token-hours: ${BMS_JWT_ACCESS_TOKEN_HOURS:24}
        # Refresh Token有效期（小时）
        refresh-token-hours: ${BMS_JWT_REFRESH_TOKEN_HOURS:48}
        # JWT发行人
        issuer: ${BMS_JWT_ISSUER:CATHAY-FUTURE-BMS-SYSTEM}
        # JWT受众
        audience: ${BMS_JWT_AUDIENCE:CATHAY-FUTURE-BMS-SYSTEM}
    # 是否使用数据库认证（false则使用固定用户认证）
    use-database-auth: ${USE_DATABASE_AUTH:true}
    # Token管理策略
    token:
      # 单用户单Token策略（推荐启用以防止Token累积）
      single-token-per-user: ${SINGLE_TOKEN_PER_USER:true}
    # 登录速率限制配置
    login-rate-limit:
      ip:
        # 每个IP每分钟最多尝试次数
        max-attempts: ${LOGIN_RATE_LIMIT_IP_MAX_ATTEMPTS:100}
        # IP限制时间窗口（分钟）
        window-minutes: ${LOGIN_RATE_LIMIT_IP_WINDOW_MINUTES:1}
        # IP锁定时间（分钟）
        lockout-minutes: ${LOGIN_RATE_LIMIT_IP_LOCKOUT_MINUTES:5}
      username:
        # 每个用户名每小时最多尝试次数
        max-attempts: ${LOGIN_RATE_LIMIT_USERNAME_MAX_ATTEMPTS:50}
        # 用户名限制时间窗口（小时）
        window-hours: ${LOGIN_RATE_LIMIT_USERNAME_WINDOW_HOURS:1}
        # 用户名锁定时间（小时）
        lockout-hours: ${LOGIN_RATE_LIMIT_USERNAME_LOCKOUT_HOURS:1}
  # API限流配置 - 使用自定义限流实现
  rate-limit:
    enabled: ${API_RATE_LIMIT_ENABLED:true}
    # 通用API限流：100次/分钟
    general:
      capacity: ${API_RATE_LIMIT_GENERAL_CAPACITY:100}
      window-seconds: ${API_RATE_LIMIT_GENERAL_WINDOW:60}
    # 高频API限流：500次/分钟
    high-frequency:
      capacity: ${API_RATE_LIMIT_HIGH_CAPACITY:500}
      window-seconds: ${API_RATE_LIMIT_HIGH_WINDOW:60}
    # 低频API限流：20次/分钟
    low-frequency:
      capacity: ${API_RATE_LIMIT_LOW_CAPACITY:20}
      window-seconds: ${API_RATE_LIMIT_LOW_WINDOW:60}
    # 排除路径
    exclude-paths:
      - /bms/auth/login
      - /actuator/**
      - /health
      - /metrics

logging:
  level:
    root: INFO
    cathayfuture.opm: DEBUG
