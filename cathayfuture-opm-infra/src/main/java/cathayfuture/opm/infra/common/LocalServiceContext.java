package cathayfuture.opm.infra.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 本地服务上下文，替代原来的ServiceContext
 * 从Spring Security的SecurityContext和配置文件获取用户信息
 * 
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@Component
public class LocalServiceContext {
    
    // 静态配置作为回退值（用于非认证场景）
    @Value("${cathay.auth.user-id:0}")
    private Long fallbackUserId;
    
    @Value("${cathay.auth.username:system}")
    private String fallbackUserCode;
    
    @Value("${cathay.auth.user-name:系统}")
    private String fallbackUserName;
    
    @Value("${cathay.auth.role-ids:0}")
    private String fallbackRoleIds;
    
    @Value("${cathay.auth.post-code:SYSTEM}")
    private String fallbackPostCode;
    
    @Value("${cathay.auth.post-name:系统}")
    private String fallbackPostName;
    
    // 提供与原ServiceContext相同的API
    public static LocalServiceContext getContext() {
        return SpringContextHolder.getBean(LocalServiceContext.class);
    }
    
    /**
     * 获取当前请求的用户ID
     * 优先从JWT token获取实际用户ID，失败时使用回退值
     */
    public Long getRequestUserId() {
        try {
            // 通过反射调用AuthenticatedUserService的静态方法
            Class<?> serviceClass = Class.forName("cathayfuture.opm.adapter.security.AuthenticatedUserService");
            Object result = serviceClass.getMethod("getAuthenticatedUserId").invoke(null);
            if (result instanceof Long) {
                Long userId = (Long) result;
                log.debug("从JWT token获取用户ID: {}", userId);
                return userId;
            }
        } catch (Exception e) {
            log.debug("从JWT token获取用户ID失败: {}", e.getMessage());
        }
        
        log.debug("使用回退用户ID: {}", fallbackUserId);
        return fallbackUserId;
    }
    
    /**
     * 获取当前请求的用户代码
     * 优先从Spring Security上下文获取，失败时使用回退值
     */
    public String getRequestUserCode() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null && auth.isAuthenticated() && !"anonymousUser".equals(auth.getPrincipal())) {
            String username = auth.getName();
            if (StringUtils.hasText(username)) {
                log.debug("从认证上下文获取用户代码: {}", username);
                return username;
            }
        }
        log.debug("未认证状态，使用回退用户代码: {}", fallbackUserCode);
        return fallbackUserCode;
    }
    
    public String getRequestTenantIdString() {
        // 返回固定的租户ID
        return "1";
    }
    
    public Long getRequestInstanceId() {
        // 返回固定的实例ID
        return 1L;
    }
    
    public void set(String key, Object value) {
        // 空实现，功能已由SecurityContext接管
        log.debug("LocalServiceContext.set() called with key: {}, value: {}", key, value);
    }
    
    public Object get(String key) {
        // 从配置文件返回固定值或从SecurityContext获取
        return getFixedValue(key);
    }
    
    public void remove(String key) {
        // 空实现，功能已由SecurityContext接管
        log.debug("LocalServiceContext.remove() called with key: {}", key);
    }
    
    private Object getFixedValue(String key) {
        // 根据key返回对应的配置值
        switch (key) {
            case ExtServiceContext.ROLE_ID:
                return fallbackRoleIds;
            case ExtServiceContext.USER_CODE:
                return fallbackUserCode;
            case ExtServiceContext.USER_NAME:
                return fallbackUserName;
            case ExtServiceContext.POST_CODE:
                return fallbackPostCode;
            case ExtServiceContext.POST_NAME:
                return fallbackPostName;
            case ExtServiceContext.USER_ID:
                return String.valueOf(fallbackUserId);
            default:
                return null;
        }
    }
}
