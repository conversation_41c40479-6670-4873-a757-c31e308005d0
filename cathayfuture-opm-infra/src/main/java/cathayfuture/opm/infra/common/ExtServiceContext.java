package cathayfuture.opm.infra.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Map;

/**
 * 描述:
 *
 * <AUTHOR>
 * @since 8/29/22
 */
@Slf4j
@Component
public class ExtServiceContext {

    private static final String BASE_KEY = "x-dtyunxi-context-";
    public static final String ROLE_ID = "yes.req.roleId";
    public static final String USER_CODE = "yes.req.userCode";
    public static final String USER_NAME = "yes.req.userName";
    public static final String POST_CODE = "yes.req.postCode";
    public static final String POST_NAME = "yes.req.postName";

    public static final String USER_ID = "yes.req.userId";

    protected static final String[] CONTEXT_KEYS = new String[]{ROLE_ID, USER_CODE, USER_NAME, POST_CODE, POST_NAME};

    // 静态配置作为回退值（用于非认证场景）
    private static String fallbackRoleIds;
    private static String fallbackUserName;
    private static String fallbackUserCode;
    private static String fallbackPostCode;
    private static String fallbackPostName;

    @Value("${cathay.auth.role-ids:0}")
    public void setFallbackRoleIds(String roleIds) {
        fallbackRoleIds = roleIds;
    }

    @Value("${cathay.auth.user-name:系统}")
    public void setFallbackUserName(String userName) {
        fallbackUserName = userName;
    }

    @Value("${cathay.auth.username:system}")
    public void setFallbackUserCode(String userCode) {
        fallbackUserCode = userCode;
    }

    @Value("${cathay.auth.post-code:SYSTEM}")
    public void setFallbackPostCode(String postCode) {
        fallbackPostCode = postCode;
    }

    @Value("${cathay.auth.post-name:系统}")
    public void setFallbackPostName(String postName) {
        fallbackPostName = postName;
    }

    /**
     * 获取当前用户的角色ID
     * 优先从JWT token获取实际角色ID，失败时使用回退值
     */
    public static String getRoleId() {
        try {
            // 通过反射调用AuthenticatedUserService的静态方法
            Class<?> serviceClass = Class.forName("cathayfuture.opm.adapter.security.AuthenticatedUserService");
            Object result = serviceClass.getMethod("getAuthenticatedUserRoleIds").invoke(null);
            if (result instanceof String && StringUtils.hasText((String) result)) {
                String roleIds = (String) result;
                log.debug("从JWT token获取用户角色ID: {}", roleIds);
                return roleIds;
            }
        } catch (Exception e) {
            log.debug("从JWT token获取角色ID失败: {}", e.getMessage());
        }
        
        log.debug("使用回退角色ID: {}", fallbackRoleIds);
        return fallbackRoleIds;
    }

    /**
     * 获取当前用户的用户代码
     * 优先从JWT token获取实际用户代码，失败时使用回退值
     */
    public static String getUserCode() {
        try {
            // 通过反射调用AuthenticatedUserService的静态方法
            Class<?> serviceClass = Class.forName("cathayfuture.opm.adapter.security.AuthenticatedUserService");
            Object result = serviceClass.getMethod("getAuthenticatedUsername").invoke(null);
            if (result instanceof String && StringUtils.hasText((String) result)) {
                String username = (String) result;
                log.debug("从JWT token获取用户代码: {}", username);
                return username;
            }
        } catch (Exception e) {
            log.debug("从JWT token获取用户代码失败: {}", e.getMessage());
        }
        
        log.debug("使用回退用户代码: {}", fallbackUserCode);
        return fallbackUserCode;
    }

    /**
     * 获取当前用户的显示名称
     * 优先从JWT token获取实际用户真实姓名，失败时使用回退值
     */
    public static String getUserName() {
        try {
            // 通过SpringContextHolder获取AuthenticatedUserService并调用静态方法
            Class<?> serviceClass = Class.forName("cathayfuture.opm.adapter.security.AuthenticatedUserService");
            Object result = serviceClass.getMethod("getAuthenticatedUserRealName").invoke(null);
            if (result instanceof String && StringUtils.hasText((String) result)) {
                String realName = (String) result;
                log.debug("从JWT token获取用户真实姓名: {}", realName);
                return realName;
            }
        } catch (Exception e) {
            log.debug("从JWT token获取用户真实姓名失败: {}", e.getMessage());
        }
        
        log.debug("使用回退用户名: {}", fallbackUserName);
        return fallbackUserName;
    }

    /**
     * 获取当前用户的岗位代码
     * 优先从JWT token获取实际岗位代码，失败时使用回退值
     */
    public static String getPostCode() {
        try {
            // 通过反射调用AuthenticatedUserService的静态方法
            Class<?> serviceClass = Class.forName("cathayfuture.opm.adapter.security.AuthenticatedUserService");
            Object result = serviceClass.getMethod("getAuthenticatedUserPostCode").invoke(null);
            if (result instanceof String && StringUtils.hasText((String) result)) {
                String postCode = (String) result;
                log.debug("从JWT token获取用户岗位代码: {}", postCode);
                return postCode;
            }
        } catch (Exception e) {
            log.debug("从JWT token获取岗位代码失败: {}", e.getMessage());
        }
        
        log.debug("使用回退岗位代码: {}", fallbackPostCode);
        return fallbackPostCode;
    }

    /**
     * 获取当前用户的岗位名称
     * 优先从JWT token获取实际岗位名称，失败时使用回退值
     */
    public static String getPostName() {
        try {
            // 通过反射调用AuthenticatedUserService的静态方法
            Class<?> serviceClass = Class.forName("cathayfuture.opm.adapter.security.AuthenticatedUserService");
            Object result = serviceClass.getMethod("getAuthenticatedUserPostName").invoke(null);
            if (result instanceof String && StringUtils.hasText((String) result)) {
                String postName = (String) result;
                log.debug("从JWT token获取用户岗位名称: {}", postName);
                return postName;
            }
        } catch (Exception e) {
            log.debug("从JWT token获取岗位名称失败: {}", e.getMessage());
        }
        
        log.debug("使用回退岗位名称: {}", fallbackPostName);
        return fallbackPostName;
    }


    public static String getHeaderKey(String key) {
        return BASE_KEY + key.toLowerCase();
    }

    // 保留向后兼容的方法，但标记为deprecated
    @Deprecated
    public static void put(String key, Object value) {
        log.warn("ExtServiceContext.put()已废弃，请使用Spring Security");
    }

    @Deprecated
    public static void init(Map<String, Object> headers) {
        log.warn("ExtServiceContext.init()已废弃，请使用Spring Security");
    }

}