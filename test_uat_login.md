# UAT 环境登录测试

## 问题修复说明

修复了 UAT 环境调用 `/bms/auth/login` 接口内部错误的问题：

### 根本原因
`LoggingAuthenticationProvider` 存在 Spring Bean 冲突：
- 同时使用 `@Component` 自动注入和手动 `new` 创建实例
- 导致多个实例冲突和依赖注入问题

### 修复措施
1. **移除 @Component 注解**：让 `LoggingAuthenticationProvider` 成为普通类
2. **简化构造函数**：移除 `@Qualifier` 注解要求
3. **优化配置逻辑**：根据配置选择使用详细日志版本或标准版本

### 测试步骤

#### 1. 启动 UAT 环境服务
```bash
# 使用 UAT 配置启动
mvn spring-boot:run -Dspring.profiles.active=uat
```

#### 2. 测试登录接口
```bash
# 使用 admin 用户登录（根据 UAT 配置中的固定用户）
curl -X POST http://localhost:8080/bms/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

#### 3. 预期结果
- **成功响应**：返回包含 token 的 JSON 响应
- **日志输出**：由于 UAT 配置中 `enable-logging-provider: true`，会有详细的认证日志

#### 4. 可能的其他测试用户
根据 UAT 配置，也可以测试：
```bash
# manager 用户
curl -X POST http://localhost:8080/bms/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "manager", 
    "password": "manager123"
  }'
```

### 日志检查
成功启动后应看到：
```
=== 开发环境安全配置 ===
当前环境: [uat]
认证模式: 开发模式（无需认证）⚠️
数据库认证: 启用（仅用于登录接口）
详细认证日志: 启用
```

### 配置变更总结
- UAT 环境：`cathay.security.debug.enable-logging-provider: true`（详细日志）
- 生产环境建议：`enable-logging-provider: false`（标准模式，性能更好）